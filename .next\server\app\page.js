/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CCharles%20Bartowski%5CDownloads%5Cmatches-headless-ui%5Cmatches-headless-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCharles%20Bartowski%5CDownloads%5Cmatches-headless-ui%5Cmatches-headless-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CCharles%20Bartowski%5CDownloads%5Cmatches-headless-ui%5Cmatches-headless-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCharles%20Bartowski%5CDownloads%5Cmatches-headless-ui%5Cmatches-headless-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?2c4b\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst page2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page2, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CCharles%20Bartowski%5CDownloads%5Cmatches-headless-ui%5Cmatches-headless-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCharles%20Bartowski%5CDownloads%5Cmatches-headless-ui%5Cmatches-headless-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Ccomponents%5C%5CSaleBanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Ccomponents%5C%5CSaleBanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ErrorBoundary.tsx */ \"(ssr)/./components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Header.tsx */ \"(ssr)/./components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/SaleBanner.tsx */ \"(ssr)/./components/SaleBanner.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Ccomponents%5C%5CSaleBanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0NoYXJsZXMlMjBCYXJ0b3dza2klNUMlNUNEb3dubG9hZHMlNUMlNUNtYXRjaGVzLWhlYWRsZXNzLXVpJTVDJTVDbWF0Y2hlcy1oZWFkbGVzcy11aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDaW1hZ2UtY29tcG9uZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0NoYXJsZXMlMjBCYXJ0b3dza2klNUMlNUNEb3dubG9hZHMlNUMlNUNtYXRjaGVzLWhlYWRsZXNzLXVpJTVDJTVDbWF0Y2hlcy1oZWFkbGVzcy11aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNOQUE0SztBQUM1SztBQUNBLGdNQUFpSyIsInNvdXJjZXMiOlsid2VicGFjazovL21hdGNoZXMtaGVhZGxlc3MvPzNjYWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxDaGFybGVzIEJhcnRvd3NraVxcXFxEb3dubG9hZHNcXFxcbWF0Y2hlcy1oZWFkbGVzcy11aVxcXFxtYXRjaGVzLWhlYWRsZXNzLXVpXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQ2hhcmxlcyBCYXJ0b3dza2lcXFxcRG93bmxvYWRzXFxcXG1hdGNoZXMtaGVhZGxlc3MtdWlcXFxcbWF0Y2hlcy1oZWFkbGVzcy11aVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCharles%20Bartowski%5C%5CDownloads%5C%5Cmatches-headless-ui%5C%5Cmatches-headless-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/ErrorBoundary.tsx":
/*!**************************************!*\
  !*** ./components/ErrorBoundary.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductErrorFallback: () => (/* binding */ ProductErrorFallback),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ProductErrorFallback,useErrorHandler,default auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props), this.retry = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined,\n                errorInfo: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        this.setState({\n            error,\n            errorInfo\n        });\n        // Log error to monitoring service\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        } else {\n            console.error('ErrorBoundary caught an error:', error, errorInfo);\n        }\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                const FallbackComponent = this.props.fallback;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                    error: this.state.error,\n                    retry: this.retry\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 16\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DefaultErrorFallback, {\n                error: this.state.error,\n                retry: this.retry\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 55,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n// Default error fallback component\nfunction DefaultErrorFallback({ error, retry }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-[400px] flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-md mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"mx-auto h-16 w-16 text-gray-400\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 1,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: \"Something went wrong\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"We apologize for the inconvenience. Please try refreshing the page.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                 true && error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mb-6 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"cursor-pointer text-sm text-gray-500 hover:text-gray-700\",\n                            children: \"Error details (development only)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 text-xs text-red-600 bg-red-50 p-3 rounded overflow-auto\",\n                            children: [\n                                error.message,\n                                error.stack && `\\n\\n${error.stack}`\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: retry,\n                            className: \"w-full bg-black text-white px-6 py-3 text-sm font-medium hover:bg-gray-800 transition-colors\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"w-full border border-gray-300 text-gray-700 px-6 py-3 text-sm font-medium hover:bg-gray-50 transition-colors\",\n                            children: \"Refresh Page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n// Specialized error fallback for product components\nfunction ProductErrorFallback({ error, retry }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-50 border border-gray-200 rounded p-6 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"mx-auto h-8 w-8 text-gray-400\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-medium text-gray-900 mb-2\",\n                children: \"Unable to load product\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xs text-gray-600 mb-4\",\n                children: \"There was an error loading this product.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: retry,\n                className: \"text-xs text-black hover:text-gray-600 underline\",\n                children: \"Try again\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n// Hook for using error boundary in functional components\nfunction useErrorHandler() {\n    return (error, errorInfo)=>{\n        console.error('Error caught by useErrorHandler:', error, errorInfo);\n    // In a real app, you might want to send this to an error reporting service\n    };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,HeartIcon,MagnifyingGlassIcon,ShoppingBagIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,HeartIcon,MagnifyingGlassIcon,ShoppingBagIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,HeartIcon,MagnifyingGlassIcon,ShoppingBagIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,HeartIcon,MagnifyingGlassIcon,ShoppingBagIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,HeartIcon,MagnifyingGlassIcon,ShoppingBagIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,HeartIcon,MagnifyingGlassIcon,ShoppingBagIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,HeartIcon,MagnifyingGlassIcon,ShoppingBagIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/data */ \"(ssr)/./lib/data.ts\");\n/* harmony import */ var _lib_hooks__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/hooks */ \"(ssr)/./lib/hooks.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isSearchOpen, setIsSearchOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [activeMegaMenu, setActiveMegaMenu] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [expandedMobileMenu, setExpandedMobileMenu] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const megaMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const isMobile = (0,_lib_hooks__WEBPACK_IMPORTED_MODULE_5__.useIsMobile)(768);\n    const { setHoverTimeout, clearHoverTimeout } = (0,_lib_hooks__WEBPACK_IMPORTED_MODULE_5__.useHoverDelay)();\n    (0,_lib_hooks__WEBPACK_IMPORTED_MODULE_5__.useClickOutside)(megaMenuRef, ()=>setActiveMegaMenu(null));\n    const handleMegaMenuEnter = (menu)=>{\n        if (!isMobile) {\n            clearHoverTimeout();\n            setActiveMegaMenu(menu);\n        }\n    };\n    const handleMegaMenuLeave = ()=>{\n        if (!isMobile) {\n            setHoverTimeout(()=>setActiveMegaMenu(null), 200);\n        }\n    };\n    const handleMegaMenuContentEnter = ()=>{\n        clearHoverTimeout();\n    };\n    const handleMegaMenuContentLeave = ()=>{\n        if (!isMobile) {\n            setHoverTimeout(()=>setActiveMegaMenu(null), 100);\n        }\n    };\n    const handleHeaderLeave = ()=>{\n        if (!isMobile) {\n            setHoverTimeout(()=>setActiveMegaMenu(null), 100);\n        }\n    };\n    const toggleMobileSubmenu = (menu)=>{\n        setExpandedMobileMenu(expandedMobileMenu === menu ? null : menu);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"bg-white border-b border-gray-200 sticky top-0 z-50\",\n            ref: megaMenuRef,\n            onMouseLeave: handleHeaderLeave,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-20 lg:h-24 xl:h-28\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                    className: \"lg:hidden p-2 text-black hover:text-gray-600 transition-colors\",\n                                    \"aria-label\": \"Toggle menu\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 65\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl md:text-2xl font-bold tracking-[0.2em] text-black\",\n                                        children: \"MATCHES\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden lg:flex items-center space-x-10 xl:space-x-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            onMouseEnter: ()=>handleMegaMenuEnter('women'),\n                                            onMouseLeave: handleMegaMenuLeave,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/women\",\n                                                className: \"text-sm font-medium text-black hover:text-gray-600 transition-colors flex items-center\",\n                                                children: [\n                                                    \"Women\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"ml-1 h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            onMouseEnter: ()=>handleMegaMenuEnter('men'),\n                                            onMouseLeave: handleMegaMenuLeave,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/men\",\n                                                className: \"text-sm font-medium text-black hover:text-gray-600 transition-colors flex items-center\",\n                                                children: [\n                                                    \"Men\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"ml-1 h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/designers\",\n                                            className: \"text-sm font-medium text-black hover:text-gray-600 transition-colors\",\n                                            children: \"Designers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/sale\",\n                                            className: \"text-sm font-medium text-red-600 hover:text-red-700 transition-colors\",\n                                            children: \"Sale\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 lg:space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsSearchOpen(!isSearchOpen),\n                                            className: \"p-2 text-black hover:text-gray-600 transition-colors lg:flex\",\n                                            \"aria-label\": \"Search\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/account\",\n                                            className: \"p-2 text-black hover:text-gray-600 transition-colors hidden lg:flex\",\n                                            \"aria-label\": \"Account\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/wishlist\",\n                                            className: \"p-2 text-black hover:text-gray-600 transition-colors relative hidden lg:flex\",\n                                            \"aria-label\": \"Wishlist\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/bag\",\n                                            className: \"p-2 text-black hover:text-gray-600 transition-colors relative\",\n                                            \"aria-label\": \"Shopping bag\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        isSearchOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-full left-0 right-0 bg-white border-t border-gray-200 py-4 z-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search for products, designers...\",\n                                                    className: \"w-full px-4 py-3 border border-gray-300 bg-white text-black focus:outline-none focus:border-black text-sm\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsSearchOpen(false),\n                                            className: \"text-sm font-medium hover:text-gray-600 text-black px-4\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                activeMegaMenu && _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-full left-0 right-0 bg-white border-t border-gray-200 shadow-xl z-40\",\n                    onMouseEnter: handleMegaMenuContentEnter,\n                    onMouseLeave: handleMegaMenuContentLeave,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-12 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu].featured.link,\n                                        className: \"group block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative aspect-[3/4] mb-4 overflow-hidden bg-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu].featured.image,\n                                                    alt: _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu].featured.title,\n                                                    fill: true,\n                                                    className: \"object-cover group-hover:scale-105 transition-transform duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-6 left-6 text-white\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold mb-2 tracking-wide\",\n                                                            children: _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu].featured.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm opacity-90\",\n                                                            children: _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu].featured.subtitle\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-8\",\n                                        children: _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu].categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: `/${activeMegaMenu}/${category.name.toLowerCase()}`,\n                                                        className: \"group block\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative aspect-[4/3] mb-3 overflow-hidden bg-gray-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: category.image,\n                                                                    alt: category.name,\n                                                                    fill: true,\n                                                                    className: \"object-cover group-hover:scale-105 transition-transform duration-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-semibold text-black uppercase tracking-wide mb-3 group-hover:text-gray-600 transition-colors\",\n                                                                children: category.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: category.items.slice(0, 6).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                href: `/${activeMegaMenu}/${category.name.toLowerCase()}/${item.toLowerCase().replace(/\\s+/g, '-')}`,\n                                                                className: \"block text-xs text-gray-600 hover:text-black transition-colors py-1\",\n                                                                children: item\n                                                            }, `${category.name}-${item}-${index}`, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, category.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-3 space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu].editorial.link,\n                                                className: \"group block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative aspect-[4/3] mb-4 overflow-hidden bg-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu].editorial.image,\n                                                            alt: _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu].editorial.title,\n                                                            fill: true,\n                                                            className: \"object-cover group-hover:scale-105 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-4 left-4 text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold mb-1 tracking-wide\",\n                                                                    children: _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu].editorial.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs opacity-90\",\n                                                                    children: _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu].editorial.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-semibold text-black uppercase tracking-wide mb-4\",\n                                                    children: \"Featured Designers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: _lib_data__WEBPACK_IMPORTED_MODULE_4__.megaMenuData[activeMegaMenu].designers.slice(0, 8).map((designer, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            href: `/designers/${designer.toLowerCase().replace(/\\s+/g, '-')}`,\n                                                            className: \"block text-xs text-gray-600 hover:text-black transition-colors py-1\",\n                                                            children: designer\n                                                        }, `${designer}-${index}`, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden absolute top-full left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>toggleMobileSubmenu('women'),\n                                        className: \"flex items-center justify-between w-full text-left py-3 text-sm font-medium text-black\",\n                                        children: [\n                                            \"Women\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: `h-4 w-4 transition-transform ${expandedMobileMenu === 'women' ? 'rotate-180' : ''}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, this),\n                                    expandedMobileMenu === 'women' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pl-4 space-y-2 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/women/new-in\",\n                                                className: \"block py-2 text-sm text-gray-600 hover:text-black\",\n                                                children: \"New In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/women/clothing\",\n                                                className: \"block py-2 text-sm text-gray-600 hover:text-black\",\n                                                children: \"Clothing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/women/shoes\",\n                                                className: \"block py-2 text-sm text-gray-600 hover:text-black\",\n                                                children: \"Shoes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/women/bags\",\n                                                className: \"block py-2 text-sm text-gray-600 hover:text-black\",\n                                                children: \"Bags\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/women/accessories\",\n                                                className: \"block py-2 text-sm text-gray-600 hover:text-black\",\n                                                children: \"Accessories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>toggleMobileSubmenu('men'),\n                                        className: \"flex items-center justify-between w-full text-left py-3 text-sm font-medium text-black\",\n                                        children: [\n                                            \"Men\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: `h-4 w-4 transition-transform ${expandedMobileMenu === 'men' ? 'rotate-180' : ''}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 17\n                                    }, this),\n                                    expandedMobileMenu === 'men' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pl-4 space-y-2 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/men/new-in\",\n                                                className: \"block py-2 text-sm text-gray-600 hover:text-black\",\n                                                children: \"New In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/men/clothing\",\n                                                className: \"block py-2 text-sm text-gray-600 hover:text-black\",\n                                                children: \"Clothing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/men/shoes\",\n                                                className: \"block py-2 text-sm text-gray-600 hover:text-black\",\n                                                children: \"Shoes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/men/bags\",\n                                                className: \"block py-2 text-sm text-gray-600 hover:text-black\",\n                                                children: \"Bags\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/men/accessories\",\n                                                className: \"block py-2 text-sm text-gray-600 hover:text-black\",\n                                                children: \"Accessories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 border-b border-gray-200 pb-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/just-in\",\n                                        className: \"block py-2 text-sm font-medium text-black\",\n                                        children: \"Just In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/designers\",\n                                        className: \"block py-2 text-sm font-medium text-black\",\n                                        children: \"Designers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/sale\",\n                                        className: \"block py-2 text-sm font-medium text-red-600\",\n                                        children: \"Sale\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/account\",\n                                        className: \"flex items-center py-2 text-sm font-medium text-black\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"My Account\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/wishlist\",\n                                        className: \"flex items-center py-2 text-sm font-medium text-black\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_HeartIcon_MagnifyingGlassIcon_ShoppingBagIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Wishlist\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Header.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/SaleBanner.tsx":
/*!***********************************!*\
  !*** ./components/SaleBanner.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SaleBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction SaleBanner() {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-black text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 lg:py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm md:text-base font-medium\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"UP TO 70% OFF \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\SaleBanner.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 13\n                            }, this),\n                            \"SALE NOW ON\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \" - LIMITED TIME ONLY\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\SaleBanner.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/sale\",\n                                className: \"ml-2 underline hover:no-underline\",\n                                children: \"SHOP NOW\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\SaleBanner.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\SaleBanner.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\SaleBanner.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\SaleBanner.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsVisible(false),\n                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 p-1 hover:bg-white/10 transition-colors\",\n                \"aria-label\": \"Close banner\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\SaleBanner.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\SaleBanner.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\SaleBanner.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/SaleBanner.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/data.ts":
/*!*********************!*\
  !*** ./lib/data.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categories: () => (/* binding */ categories),\n/* harmony export */   filterOptions: () => (/* binding */ filterOptions),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductsByCategory: () => (/* binding */ getProductsByCategory),\n/* harmony export */   megaMenuData: () => (/* binding */ megaMenuData),\n/* harmony export */   mockProducts: () => (/* binding */ mockProducts),\n/* harmony export */   searchProducts: () => (/* binding */ searchProducts)\n/* harmony export */ });\n// Centralized data management for the Matches Headless UI\n// Mock product data - in production this would come from WooCommerce API\nconst mockProducts = [\n    {\n        id: '1',\n        name: 'Moon oversized cotton-poplin shirt',\n        brand: 'The Row',\n        price: '£1,200',\n        image: '/products/the-row-shirt.jpg',\n        isOnSale: false,\n        colors: [\n            'White',\n            'Black',\n            'Navy'\n        ],\n        sizes: [\n            'XS',\n            'S',\n            'M',\n            'L',\n            'XL'\n        ]\n    },\n    {\n        id: '2',\n        name: 'Cashmere sweater',\n        brand: 'Brunello Cucinelli',\n        price: '£890',\n        image: '/products/brunello-sweater.jpg',\n        isOnSale: false,\n        colors: [\n            'Beige',\n            'Grey',\n            'Navy'\n        ]\n    },\n    {\n        id: '3',\n        name: 'Wool coat',\n        brand: 'Max Mara',\n        price: '£1,450',\n        originalPrice: '£1,800',\n        image: '/products/maxmara-coat.jpg',\n        isOnSale: true,\n        colors: [\n            'Camel',\n            'Black',\n            'Navy'\n        ]\n    },\n    {\n        id: '4',\n        name: 'Silk dress',\n        brand: 'Bottega Veneta',\n        price: '£2,100',\n        image: '/products/bottega-dress.jpg',\n        isOnSale: false,\n        colors: [\n            'Black',\n            'Green',\n            'Brown'\n        ]\n    },\n    {\n        id: '5',\n        name: 'Leather jacket',\n        brand: 'Saint Laurent',\n        price: '£3,200',\n        image: '/products/saint-laurent-jacket.jpg',\n        isOnSale: false,\n        colors: [\n            'Black'\n        ]\n    },\n    {\n        id: '6',\n        name: 'Knit sweater',\n        brand: 'Ganni',\n        price: '£295',\n        image: '/products/ganni-sweater.jpg',\n        isOnSale: false,\n        colors: [\n            'Pink',\n            'Blue',\n            'Yellow'\n        ]\n    }\n];\n// Category data\nconst categories = [\n    {\n        id: 1,\n        name: 'CLOTHING',\n        slug: 'clothing',\n        count: '545 PRODUCTS',\n        image: '/category-clothing.jpg',\n        href: '/clothing'\n    },\n    {\n        id: 2,\n        name: 'SHOES',\n        slug: 'shoes',\n        count: '234 PRODUCTS',\n        image: '/category-shoes.jpg',\n        href: '/shoes'\n    },\n    {\n        id: 3,\n        name: 'BAGS',\n        slug: 'bags',\n        count: '156 PRODUCTS',\n        image: '/category-bags.jpg',\n        href: '/bags'\n    },\n    {\n        id: 4,\n        name: 'ACCESSORIES',\n        slug: 'accessories',\n        count: '89 PRODUCTS',\n        image: '/category-accessories.jpg',\n        href: '/accessories'\n    },\n    {\n        id: 5,\n        name: 'SALE',\n        slug: 'sale',\n        count: 'UP TO 70% OFF',\n        image: '/category-sale.jpg',\n        href: '/sale'\n    }\n];\n// Filter options\nconst filterOptions = [\n    {\n        name: 'Categories',\n        options: [\n            'Clothing',\n            'Shoes',\n            'Bags',\n            'Accessories'\n        ]\n    },\n    {\n        name: 'Designer',\n        options: [\n            'Bottega Veneta',\n            'Gucci',\n            'Prada',\n            'Saint Laurent',\n            'The Row',\n            'Brunello Cucinelli',\n            'Max Mara',\n            'Ganni'\n        ]\n    },\n    {\n        name: 'Colour',\n        options: [\n            'Black',\n            'White',\n            'Brown',\n            'Blue',\n            'Green',\n            'Red',\n            'Pink',\n            'Yellow',\n            'Beige',\n            'Grey',\n            'Navy',\n            'Camel'\n        ]\n    },\n    {\n        name: 'Clothes Size',\n        options: [\n            'XXS',\n            'XS',\n            'S',\n            'M',\n            'L',\n            'XL',\n            'XXL'\n        ]\n    },\n    {\n        name: 'Shoe Sizes',\n        options: [\n            '5',\n            '6',\n            '7',\n            '8',\n            '9',\n            '10',\n            '11'\n        ]\n    },\n    {\n        name: 'Shop By',\n        options: [\n            'New Arrivals',\n            'Sale',\n            'Just In',\n            'Trending'\n        ]\n    },\n    {\n        name: 'Price',\n        options: [\n            'Under £100',\n            '£100 - £500',\n            '£500 - £1000',\n            'Over £1000'\n        ]\n    }\n];\n// Mega menu data\nconst megaMenuData = {\n    women: {\n        featured: {\n            title: 'NEW SEASON ARRIVALS',\n            subtitle: 'Discover the latest from your favorite designers',\n            image: '/hero-image.jpg',\n            link: '/women/new-arrivals'\n        },\n        categories: [\n            {\n                name: 'Clothing',\n                image: '/category-clothing.jpg',\n                items: [\n                    'Shop All',\n                    'Dresses',\n                    'Knitwear',\n                    'Coats',\n                    'Jackets',\n                    'Jeans',\n                    'Jumpsuits',\n                    'Lingerie',\n                    'Skirts',\n                    'Suits',\n                    'Swimwear',\n                    'Tops',\n                    'Trousers'\n                ]\n            },\n            {\n                name: 'Shoes',\n                image: '/category-shoes.jpg',\n                items: [\n                    'Shop All',\n                    'Boots',\n                    'Flats',\n                    'Heels',\n                    'Sandals',\n                    'Sneakers',\n                    'Trainers'\n                ]\n            },\n            {\n                name: 'Bags',\n                image: '/category-bags.jpg',\n                items: [\n                    'Shop All',\n                    'Backpacks',\n                    'Belt Bags',\n                    'Clutches',\n                    'Cross-body',\n                    'Handbags',\n                    'Shoulder Bags',\n                    'Totes',\n                    'Travel'\n                ]\n            },\n            {\n                name: 'Accessories',\n                image: '/category-accessories.jpg',\n                items: [\n                    'Shop All',\n                    'Belts',\n                    'Gloves',\n                    'Hair Accessories',\n                    'Hats',\n                    'Jewelry & Watches',\n                    'Scarves',\n                    'Sunglasses',\n                    'Tech Accessories'\n                ]\n            }\n        ],\n        editorial: {\n            title: 'WINTER ESSENTIALS',\n            subtitle: 'Curated edit for the season',\n            image: '/editorial-1.jpg',\n            link: '/women/winter-essentials'\n        },\n        designers: [\n            'Acne Studios',\n            'Bottega Veneta',\n            'Ganni',\n            'Jacquemus',\n            'Khaite',\n            'Loewe',\n            'Prada',\n            'Saint Laurent',\n            'The Row',\n            'Toteme'\n        ]\n    },\n    men: {\n        featured: {\n            title: 'NEW SEASON ARRIVALS',\n            subtitle: 'Discover the latest from your favorite designers',\n            image: '/hero-image.jpg',\n            link: '/men/new-arrivals'\n        },\n        categories: [\n            {\n                name: 'Clothing',\n                image: '/category-clothing.jpg',\n                items: [\n                    'Shop All',\n                    'Blazers',\n                    'Coats',\n                    'Hoodies',\n                    'Jackets',\n                    'Jeans',\n                    'Knitwear',\n                    'Polo Shirts',\n                    'Shirts',\n                    'Shorts',\n                    'Suits',\n                    'Sweatshirts',\n                    'T-Shirts',\n                    'Trousers'\n                ]\n            },\n            {\n                name: 'Shoes',\n                image: '/category-shoes.jpg',\n                items: [\n                    'Shop All',\n                    'Boots',\n                    'Dress Shoes',\n                    'Loafers',\n                    'Sandals',\n                    'Sneakers',\n                    'Trainers'\n                ]\n            },\n            {\n                name: 'Bags',\n                image: '/category-bags.jpg',\n                items: [\n                    'Shop All',\n                    'Backpacks',\n                    'Belt Bags',\n                    'Briefcases',\n                    'Cross-body',\n                    'Messenger',\n                    'Totes',\n                    'Travel'\n                ]\n            },\n            {\n                name: 'Accessories',\n                image: '/category-accessories.jpg',\n                items: [\n                    'Shop All',\n                    'Belts',\n                    'Gloves',\n                    'Hats',\n                    'Jewelry & Watches',\n                    'Scarves',\n                    'Sunglasses',\n                    'Tech Accessories',\n                    'Ties',\n                    'Wallets'\n                ]\n            }\n        ],\n        editorial: {\n            title: 'MODERN CLASSICS',\n            subtitle: 'Timeless pieces for the modern man',\n            image: '/editorial-2.jpg',\n            link: '/men/modern-classics'\n        },\n        designers: [\n            'Acne Studios',\n            'Bottega Veneta',\n            'Fear of God',\n            'Jacquemus',\n            'Kenzo',\n            'Off-White',\n            'Stone Island',\n            'Thom Browne',\n            'The Row',\n            'Valentino'\n        ]\n    }\n};\n// Utility functions\nconst formatPrice = (price)=>{\n    if (typeof price === 'string') {\n        return price.startsWith('£') ? price : `£${price}`;\n    }\n    return `£${price.toLocaleString()}`;\n};\nconst getProductsByCategory = (categorySlug)=>{\n    // In production, this would filter products from API\n    return mockProducts.filter((product)=>product.categories?.some((cat)=>cat.slug === categorySlug));\n};\nconst getProductById = (id)=>{\n    return mockProducts.find((product)=>product.id === id);\n};\nconst searchProducts = (query)=>{\n    const lowercaseQuery = query.toLowerCase();\n    return mockProducts.filter((product)=>product.name.toLowerCase().includes(lowercaseQuery) || product.brand.toLowerCase().includes(lowercaseQuery));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvZGF0YS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBLDBEQUEwRDtBQUcxRCx5RUFBeUU7QUFDbEUsTUFBTUEsZUFBMEI7SUFDckM7UUFDRUMsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsUUFBUTtZQUFDO1lBQVM7WUFBUztTQUFPO1FBQ2xDQyxPQUFPO1lBQUM7WUFBTTtZQUFLO1lBQUs7WUFBSztTQUFLO0lBQ3BDO0lBQ0E7UUFDRVAsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsUUFBUTtZQUFDO1lBQVM7WUFBUTtTQUFPO0lBQ25DO0lBQ0E7UUFDRU4sSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsT0FBTztRQUNQSyxlQUFlO1FBQ2ZKLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxRQUFRO1lBQUM7WUFBUztZQUFTO1NBQU87SUFDcEM7SUFDQTtRQUNFTixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxRQUFRO1lBQUM7WUFBUztZQUFTO1NBQVE7SUFDckM7SUFDQTtRQUNFTixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxRQUFRO1lBQUM7U0FBUTtJQUNuQjtJQUNBO1FBQ0VOLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFFBQVE7WUFBQztZQUFRO1lBQVE7U0FBUztJQUNwQztDQUNEO0FBRUQsZ0JBQWdCO0FBQ1QsTUFBTUcsYUFBeUI7SUFDcEM7UUFDRVQsSUFBSTtRQUNKQyxNQUFNO1FBQ05TLE1BQU07UUFDTkMsT0FBTztRQUNQUCxPQUFPO1FBQ1BRLE1BQU07SUFDUjtJQUNBO1FBQ0VaLElBQUk7UUFDSkMsTUFBTTtRQUNOUyxNQUFNO1FBQ05DLE9BQU87UUFDUFAsT0FBTztRQUNQUSxNQUFNO0lBQ1I7SUFDQTtRQUNFWixJQUFJO1FBQ0pDLE1BQU07UUFDTlMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BQLE9BQU87UUFDUFEsTUFBTTtJQUNSO0lBQ0E7UUFDRVosSUFBSTtRQUNKQyxNQUFNO1FBQ05TLE1BQU07UUFDTkMsT0FBTztRQUNQUCxPQUFPO1FBQ1BRLE1BQU07SUFDUjtJQUNBO1FBQ0VaLElBQUk7UUFDSkMsTUFBTTtRQUNOUyxNQUFNO1FBQ05DLE9BQU87UUFDUFAsT0FBTztRQUNQUSxNQUFNO0lBQ1I7Q0FDRDtBQUVELGlCQUFpQjtBQUNWLE1BQU1DLGdCQUFnQztJQUMzQztRQUNFWixNQUFNO1FBQ05hLFNBQVM7WUFBQztZQUFZO1lBQVM7WUFBUTtTQUFjO0lBQ3ZEO0lBQ0E7UUFDRWIsTUFBTTtRQUNOYSxTQUFTO1lBQUM7WUFBa0I7WUFBUztZQUFTO1lBQWlCO1lBQVc7WUFBc0I7WUFBWTtTQUFRO0lBQ3RIO0lBQ0E7UUFDRWIsTUFBTTtRQUNOYSxTQUFTO1lBQUM7WUFBUztZQUFTO1lBQVM7WUFBUTtZQUFTO1lBQU87WUFBUTtZQUFVO1lBQVM7WUFBUTtZQUFRO1NBQVE7SUFDbEg7SUFDQTtRQUNFYixNQUFNO1FBQ05hLFNBQVM7WUFBQztZQUFPO1lBQU07WUFBSztZQUFLO1lBQUs7WUFBTTtTQUFNO0lBQ3BEO0lBQ0E7UUFDRWIsTUFBTTtRQUNOYSxTQUFTO1lBQUM7WUFBSztZQUFLO1lBQUs7WUFBSztZQUFLO1lBQU07U0FBSztJQUNoRDtJQUNBO1FBQ0ViLE1BQU07UUFDTmEsU0FBUztZQUFDO1lBQWdCO1lBQVE7WUFBVztTQUFXO0lBQzFEO0lBQ0E7UUFDRWIsTUFBTTtRQUNOYSxTQUFTO1lBQUM7WUFBYztZQUFlO1lBQWdCO1NBQWE7SUFDdEU7Q0FDRDtBQUVELGlCQUFpQjtBQUNWLE1BQU1DLGVBQTZDO0lBQ3hEQyxPQUFPO1FBQ0xDLFVBQVU7WUFDUkMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZmLE9BQU87WUFDUGdCLE1BQU07UUFDUjtRQUNBWCxZQUFZO1lBQ1Y7Z0JBQ0VSLE1BQU07Z0JBQ05HLE9BQU87Z0JBQ1BpQixPQUFPO29CQUFDO29CQUFZO29CQUFXO29CQUFZO29CQUFTO29CQUFXO29CQUFTO29CQUFhO29CQUFZO29CQUFVO29CQUFTO29CQUFZO29CQUFRO2lCQUFXO1lBQ3JKO1lBQ0E7Z0JBQ0VwQixNQUFNO2dCQUNORyxPQUFPO2dCQUNQaUIsT0FBTztvQkFBQztvQkFBWTtvQkFBUztvQkFBUztvQkFBUztvQkFBVztvQkFBWTtpQkFBVztZQUNuRjtZQUNBO2dCQUNFcEIsTUFBTTtnQkFDTkcsT0FBTztnQkFDUGlCLE9BQU87b0JBQUM7b0JBQVk7b0JBQWE7b0JBQWE7b0JBQVk7b0JBQWM7b0JBQVk7b0JBQWlCO29CQUFTO2lCQUFTO1lBQ3pIO1lBQ0E7Z0JBQ0VwQixNQUFNO2dCQUNORyxPQUFPO2dCQUNQaUIsT0FBTztvQkFBQztvQkFBWTtvQkFBUztvQkFBVTtvQkFBb0I7b0JBQVE7b0JBQXFCO29CQUFXO29CQUFjO2lCQUFtQjtZQUN0STtTQUNEO1FBQ0RDLFdBQVc7WUFDVEosT0FBTztZQUNQQyxVQUFVO1lBQ1ZmLE9BQU87WUFDUGdCLE1BQU07UUFDUjtRQUNBRyxXQUFXO1lBQUM7WUFBZ0I7WUFBa0I7WUFBUztZQUFhO1lBQVU7WUFBUztZQUFTO1lBQWlCO1lBQVc7U0FBUztJQUN2STtJQUNBQyxLQUFLO1FBQ0hQLFVBQVU7WUFDUkMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZmLE9BQU87WUFDUGdCLE1BQU07UUFDUjtRQUNBWCxZQUFZO1lBQ1Y7Z0JBQ0VSLE1BQU07Z0JBQ05HLE9BQU87Z0JBQ1BpQixPQUFPO29CQUFDO29CQUFZO29CQUFXO29CQUFTO29CQUFXO29CQUFXO29CQUFTO29CQUFZO29CQUFlO29CQUFVO29CQUFVO29CQUFTO29CQUFlO29CQUFZO2lCQUFXO1lBQ3ZLO1lBQ0E7Z0JBQ0VwQixNQUFNO2dCQUNORyxPQUFPO2dCQUNQaUIsT0FBTztvQkFBQztvQkFBWTtvQkFBUztvQkFBZTtvQkFBVztvQkFBVztvQkFBWTtpQkFBVztZQUMzRjtZQUNBO2dCQUNFcEIsTUFBTTtnQkFDTkcsT0FBTztnQkFDUGlCLE9BQU87b0JBQUM7b0JBQVk7b0JBQWE7b0JBQWE7b0JBQWM7b0JBQWM7b0JBQWE7b0JBQVM7aUJBQVM7WUFDM0c7WUFDQTtnQkFDRXBCLE1BQU07Z0JBQ05HLE9BQU87Z0JBQ1BpQixPQUFPO29CQUFDO29CQUFZO29CQUFTO29CQUFVO29CQUFRO29CQUFxQjtvQkFBVztvQkFBYztvQkFBb0I7b0JBQVE7aUJBQVU7WUFDckk7U0FDRDtRQUNEQyxXQUFXO1lBQ1RKLE9BQU87WUFDUEMsVUFBVTtZQUNWZixPQUFPO1lBQ1BnQixNQUFNO1FBQ1I7UUFDQUcsV0FBVztZQUFDO1lBQWdCO1lBQWtCO1lBQWU7WUFBYTtZQUFTO1lBQWE7WUFBZ0I7WUFBZTtZQUFXO1NBQVk7SUFDeEo7QUFDRixFQUFDO0FBRUQsb0JBQW9CO0FBQ2IsTUFBTUUsY0FBYyxDQUFDdEI7SUFDMUIsSUFBSSxPQUFPQSxVQUFVLFVBQVU7UUFDN0IsT0FBT0EsTUFBTXVCLFVBQVUsQ0FBQyxPQUFPdkIsUUFBUSxDQUFDLENBQUMsRUFBRUEsT0FBTztJQUNwRDtJQUNBLE9BQU8sQ0FBQyxDQUFDLEVBQUVBLE1BQU13QixjQUFjLElBQUk7QUFDckMsRUFBQztBQUVNLE1BQU1DLHdCQUF3QixDQUFDQztJQUNwQyxxREFBcUQ7SUFDckQsT0FBTzlCLGFBQWErQixNQUFNLENBQUNDLENBQUFBLFVBQ3pCQSxRQUFRdEIsVUFBVSxFQUFFdUIsS0FBS0MsQ0FBQUEsTUFBT0EsSUFBSXZCLElBQUksS0FBS21CO0FBRWpELEVBQUM7QUFFTSxNQUFNSyxpQkFBaUIsQ0FBQ2xDO0lBQzdCLE9BQU9ELGFBQWFvQyxJQUFJLENBQUNKLENBQUFBLFVBQVdBLFFBQVEvQixFQUFFLEtBQUtBO0FBQ3JELEVBQUM7QUFFTSxNQUFNb0MsaUJBQWlCLENBQUNDO0lBQzdCLE1BQU1DLGlCQUFpQkQsTUFBTUUsV0FBVztJQUN4QyxPQUFPeEMsYUFBYStCLE1BQU0sQ0FBQ0MsQ0FBQUEsVUFDekJBLFFBQVE5QixJQUFJLENBQUNzQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0YsbUJBQ3BDUCxRQUFRN0IsS0FBSyxDQUFDcUMsV0FBVyxHQUFHQyxRQUFRLENBQUNGO0FBRXpDLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXRjaGVzLWhlYWRsZXNzLy4vbGliL2RhdGEudHM/MTZmMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDZW50cmFsaXplZCBkYXRhIG1hbmFnZW1lbnQgZm9yIHRoZSBNYXRjaGVzIEhlYWRsZXNzIFVJXG5pbXBvcnQgeyBQcm9kdWN0LCBDYXRlZ29yeSwgTWVnYU1lbnVEYXRhLCBGaWx0ZXJPcHRpb24gfSBmcm9tICcuL3R5cGVzJ1xuXG4vLyBNb2NrIHByb2R1Y3QgZGF0YSAtIGluIHByb2R1Y3Rpb24gdGhpcyB3b3VsZCBjb21lIGZyb20gV29vQ29tbWVyY2UgQVBJXG5leHBvcnQgY29uc3QgbW9ja1Byb2R1Y3RzOiBQcm9kdWN0W10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIG5hbWU6ICdNb29uIG92ZXJzaXplZCBjb3R0b24tcG9wbGluIHNoaXJ0JyxcbiAgICBicmFuZDogJ1RoZSBSb3cnLFxuICAgIHByaWNlOiAnwqMxLDIwMCcsXG4gICAgaW1hZ2U6ICcvcHJvZHVjdHMvdGhlLXJvdy1zaGlydC5qcGcnLFxuICAgIGlzT25TYWxlOiBmYWxzZSxcbiAgICBjb2xvcnM6IFsnV2hpdGUnLCAnQmxhY2snLCAnTmF2eSddLFxuICAgIHNpemVzOiBbJ1hTJywgJ1MnLCAnTScsICdMJywgJ1hMJ11cbiAgfSxcbiAge1xuICAgIGlkOiAnMicsXG4gICAgbmFtZTogJ0Nhc2htZXJlIHN3ZWF0ZXInLFxuICAgIGJyYW5kOiAnQnJ1bmVsbG8gQ3VjaW5lbGxpJyxcbiAgICBwcmljZTogJ8KjODkwJyxcbiAgICBpbWFnZTogJy9wcm9kdWN0cy9icnVuZWxsby1zd2VhdGVyLmpwZycsXG4gICAgaXNPblNhbGU6IGZhbHNlLFxuICAgIGNvbG9yczogWydCZWlnZScsICdHcmV5JywgJ05hdnknXVxuICB9LFxuICB7XG4gICAgaWQ6ICczJyxcbiAgICBuYW1lOiAnV29vbCBjb2F0JyxcbiAgICBicmFuZDogJ01heCBNYXJhJyxcbiAgICBwcmljZTogJ8KjMSw0NTAnLFxuICAgIG9yaWdpbmFsUHJpY2U6ICfCozEsODAwJyxcbiAgICBpbWFnZTogJy9wcm9kdWN0cy9tYXhtYXJhLWNvYXQuanBnJyxcbiAgICBpc09uU2FsZTogdHJ1ZSxcbiAgICBjb2xvcnM6IFsnQ2FtZWwnLCAnQmxhY2snLCAnTmF2eSddXG4gIH0sXG4gIHtcbiAgICBpZDogJzQnLFxuICAgIG5hbWU6ICdTaWxrIGRyZXNzJyxcbiAgICBicmFuZDogJ0JvdHRlZ2EgVmVuZXRhJyxcbiAgICBwcmljZTogJ8KjMiwxMDAnLFxuICAgIGltYWdlOiAnL3Byb2R1Y3RzL2JvdHRlZ2EtZHJlc3MuanBnJyxcbiAgICBpc09uU2FsZTogZmFsc2UsXG4gICAgY29sb3JzOiBbJ0JsYWNrJywgJ0dyZWVuJywgJ0Jyb3duJ11cbiAgfSxcbiAge1xuICAgIGlkOiAnNScsXG4gICAgbmFtZTogJ0xlYXRoZXIgamFja2V0JyxcbiAgICBicmFuZDogJ1NhaW50IExhdXJlbnQnLFxuICAgIHByaWNlOiAnwqMzLDIwMCcsXG4gICAgaW1hZ2U6ICcvcHJvZHVjdHMvc2FpbnQtbGF1cmVudC1qYWNrZXQuanBnJyxcbiAgICBpc09uU2FsZTogZmFsc2UsXG4gICAgY29sb3JzOiBbJ0JsYWNrJ11cbiAgfSxcbiAge1xuICAgIGlkOiAnNicsXG4gICAgbmFtZTogJ0tuaXQgc3dlYXRlcicsXG4gICAgYnJhbmQ6ICdHYW5uaScsXG4gICAgcHJpY2U6ICfCozI5NScsXG4gICAgaW1hZ2U6ICcvcHJvZHVjdHMvZ2Fubmktc3dlYXRlci5qcGcnLFxuICAgIGlzT25TYWxlOiBmYWxzZSxcbiAgICBjb2xvcnM6IFsnUGluaycsICdCbHVlJywgJ1llbGxvdyddXG4gIH1cbl1cblxuLy8gQ2F0ZWdvcnkgZGF0YVxuZXhwb3J0IGNvbnN0IGNhdGVnb3JpZXM6IENhdGVnb3J5W10gPSBbXG4gIHtcbiAgICBpZDogMSxcbiAgICBuYW1lOiAnQ0xPVEhJTkcnLFxuICAgIHNsdWc6ICdjbG90aGluZycsXG4gICAgY291bnQ6ICc1NDUgUFJPRFVDVFMnLFxuICAgIGltYWdlOiAnL2NhdGVnb3J5LWNsb3RoaW5nLmpwZycsXG4gICAgaHJlZjogJy9jbG90aGluZydcbiAgfSxcbiAge1xuICAgIGlkOiAyLFxuICAgIG5hbWU6ICdTSE9FUycsXG4gICAgc2x1ZzogJ3Nob2VzJyxcbiAgICBjb3VudDogJzIzNCBQUk9EVUNUUycsXG4gICAgaW1hZ2U6ICcvY2F0ZWdvcnktc2hvZXMuanBnJyxcbiAgICBocmVmOiAnL3Nob2VzJ1xuICB9LFxuICB7XG4gICAgaWQ6IDMsXG4gICAgbmFtZTogJ0JBR1MnLFxuICAgIHNsdWc6ICdiYWdzJyxcbiAgICBjb3VudDogJzE1NiBQUk9EVUNUUycsXG4gICAgaW1hZ2U6ICcvY2F0ZWdvcnktYmFncy5qcGcnLFxuICAgIGhyZWY6ICcvYmFncydcbiAgfSxcbiAge1xuICAgIGlkOiA0LFxuICAgIG5hbWU6ICdBQ0NFU1NPUklFUycsXG4gICAgc2x1ZzogJ2FjY2Vzc29yaWVzJyxcbiAgICBjb3VudDogJzg5IFBST0RVQ1RTJyxcbiAgICBpbWFnZTogJy9jYXRlZ29yeS1hY2Nlc3Nvcmllcy5qcGcnLFxuICAgIGhyZWY6ICcvYWNjZXNzb3JpZXMnXG4gIH0sXG4gIHtcbiAgICBpZDogNSxcbiAgICBuYW1lOiAnU0FMRScsXG4gICAgc2x1ZzogJ3NhbGUnLFxuICAgIGNvdW50OiAnVVAgVE8gNzAlIE9GRicsXG4gICAgaW1hZ2U6ICcvY2F0ZWdvcnktc2FsZS5qcGcnLFxuICAgIGhyZWY6ICcvc2FsZSdcbiAgfVxuXVxuXG4vLyBGaWx0ZXIgb3B0aW9uc1xuZXhwb3J0IGNvbnN0IGZpbHRlck9wdGlvbnM6IEZpbHRlck9wdGlvbltdID0gW1xuICB7XG4gICAgbmFtZTogJ0NhdGVnb3JpZXMnLFxuICAgIG9wdGlvbnM6IFsnQ2xvdGhpbmcnLCAnU2hvZXMnLCAnQmFncycsICdBY2Nlc3NvcmllcyddXG4gIH0sXG4gIHtcbiAgICBuYW1lOiAnRGVzaWduZXInLFxuICAgIG9wdGlvbnM6IFsnQm90dGVnYSBWZW5ldGEnLCAnR3VjY2knLCAnUHJhZGEnLCAnU2FpbnQgTGF1cmVudCcsICdUaGUgUm93JywgJ0JydW5lbGxvIEN1Y2luZWxsaScsICdNYXggTWFyYScsICdHYW5uaSddXG4gIH0sXG4gIHtcbiAgICBuYW1lOiAnQ29sb3VyJyxcbiAgICBvcHRpb25zOiBbJ0JsYWNrJywgJ1doaXRlJywgJ0Jyb3duJywgJ0JsdWUnLCAnR3JlZW4nLCAnUmVkJywgJ1BpbmsnLCAnWWVsbG93JywgJ0JlaWdlJywgJ0dyZXknLCAnTmF2eScsICdDYW1lbCddXG4gIH0sXG4gIHtcbiAgICBuYW1lOiAnQ2xvdGhlcyBTaXplJyxcbiAgICBvcHRpb25zOiBbJ1hYUycsICdYUycsICdTJywgJ00nLCAnTCcsICdYTCcsICdYWEwnXVxuICB9LFxuICB7XG4gICAgbmFtZTogJ1Nob2UgU2l6ZXMnLFxuICAgIG9wdGlvbnM6IFsnNScsICc2JywgJzcnLCAnOCcsICc5JywgJzEwJywgJzExJ11cbiAgfSxcbiAge1xuICAgIG5hbWU6ICdTaG9wIEJ5JyxcbiAgICBvcHRpb25zOiBbJ05ldyBBcnJpdmFscycsICdTYWxlJywgJ0p1c3QgSW4nLCAnVHJlbmRpbmcnXVxuICB9LFxuICB7XG4gICAgbmFtZTogJ1ByaWNlJyxcbiAgICBvcHRpb25zOiBbJ1VuZGVyIMKjMTAwJywgJ8KjMTAwIC0gwqM1MDAnLCAnwqM1MDAgLSDCozEwMDAnLCAnT3ZlciDCozEwMDAnXVxuICB9XG5dXG5cbi8vIE1lZ2EgbWVudSBkYXRhXG5leHBvcnQgY29uc3QgbWVnYU1lbnVEYXRhOiBSZWNvcmQ8c3RyaW5nLCBNZWdhTWVudURhdGE+ID0ge1xuICB3b21lbjoge1xuICAgIGZlYXR1cmVkOiB7XG4gICAgICB0aXRsZTogJ05FVyBTRUFTT04gQVJSSVZBTFMnLFxuICAgICAgc3VidGl0bGU6ICdEaXNjb3ZlciB0aGUgbGF0ZXN0IGZyb20geW91ciBmYXZvcml0ZSBkZXNpZ25lcnMnLFxuICAgICAgaW1hZ2U6ICcvaGVyby1pbWFnZS5qcGcnLFxuICAgICAgbGluazogJy93b21lbi9uZXctYXJyaXZhbHMnXG4gICAgfSxcbiAgICBjYXRlZ29yaWVzOiBbXG4gICAgICB7XG4gICAgICAgIG5hbWU6ICdDbG90aGluZycsXG4gICAgICAgIGltYWdlOiAnL2NhdGVnb3J5LWNsb3RoaW5nLmpwZycsXG4gICAgICAgIGl0ZW1zOiBbJ1Nob3AgQWxsJywgJ0RyZXNzZXMnLCAnS25pdHdlYXInLCAnQ29hdHMnLCAnSmFja2V0cycsICdKZWFucycsICdKdW1wc3VpdHMnLCAnTGluZ2VyaWUnLCAnU2tpcnRzJywgJ1N1aXRzJywgJ1N3aW13ZWFyJywgJ1RvcHMnLCAnVHJvdXNlcnMnXVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgbmFtZTogJ1Nob2VzJyxcbiAgICAgICAgaW1hZ2U6ICcvY2F0ZWdvcnktc2hvZXMuanBnJyxcbiAgICAgICAgaXRlbXM6IFsnU2hvcCBBbGwnLCAnQm9vdHMnLCAnRmxhdHMnLCAnSGVlbHMnLCAnU2FuZGFscycsICdTbmVha2VycycsICdUcmFpbmVycyddXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBuYW1lOiAnQmFncycsXG4gICAgICAgIGltYWdlOiAnL2NhdGVnb3J5LWJhZ3MuanBnJyxcbiAgICAgICAgaXRlbXM6IFsnU2hvcCBBbGwnLCAnQmFja3BhY2tzJywgJ0JlbHQgQmFncycsICdDbHV0Y2hlcycsICdDcm9zcy1ib2R5JywgJ0hhbmRiYWdzJywgJ1Nob3VsZGVyIEJhZ3MnLCAnVG90ZXMnLCAnVHJhdmVsJ11cbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIG5hbWU6ICdBY2Nlc3NvcmllcycsXG4gICAgICAgIGltYWdlOiAnL2NhdGVnb3J5LWFjY2Vzc29yaWVzLmpwZycsXG4gICAgICAgIGl0ZW1zOiBbJ1Nob3AgQWxsJywgJ0JlbHRzJywgJ0dsb3ZlcycsICdIYWlyIEFjY2Vzc29yaWVzJywgJ0hhdHMnLCAnSmV3ZWxyeSAmIFdhdGNoZXMnLCAnU2NhcnZlcycsICdTdW5nbGFzc2VzJywgJ1RlY2ggQWNjZXNzb3JpZXMnXVxuICAgICAgfVxuICAgIF0sXG4gICAgZWRpdG9yaWFsOiB7XG4gICAgICB0aXRsZTogJ1dJTlRFUiBFU1NFTlRJQUxTJyxcbiAgICAgIHN1YnRpdGxlOiAnQ3VyYXRlZCBlZGl0IGZvciB0aGUgc2Vhc29uJyxcbiAgICAgIGltYWdlOiAnL2VkaXRvcmlhbC0xLmpwZycsXG4gICAgICBsaW5rOiAnL3dvbWVuL3dpbnRlci1lc3NlbnRpYWxzJ1xuICAgIH0sXG4gICAgZGVzaWduZXJzOiBbJ0FjbmUgU3R1ZGlvcycsICdCb3R0ZWdhIFZlbmV0YScsICdHYW5uaScsICdKYWNxdWVtdXMnLCAnS2hhaXRlJywgJ0xvZXdlJywgJ1ByYWRhJywgJ1NhaW50IExhdXJlbnQnLCAnVGhlIFJvdycsICdUb3RlbWUnXVxuICB9LFxuICBtZW46IHtcbiAgICBmZWF0dXJlZDoge1xuICAgICAgdGl0bGU6ICdORVcgU0VBU09OIEFSUklWQUxTJyxcbiAgICAgIHN1YnRpdGxlOiAnRGlzY292ZXIgdGhlIGxhdGVzdCBmcm9tIHlvdXIgZmF2b3JpdGUgZGVzaWduZXJzJyxcbiAgICAgIGltYWdlOiAnL2hlcm8taW1hZ2UuanBnJyxcbiAgICAgIGxpbms6ICcvbWVuL25ldy1hcnJpdmFscydcbiAgICB9LFxuICAgIGNhdGVnb3JpZXM6IFtcbiAgICAgIHtcbiAgICAgICAgbmFtZTogJ0Nsb3RoaW5nJyxcbiAgICAgICAgaW1hZ2U6ICcvY2F0ZWdvcnktY2xvdGhpbmcuanBnJyxcbiAgICAgICAgaXRlbXM6IFsnU2hvcCBBbGwnLCAnQmxhemVycycsICdDb2F0cycsICdIb29kaWVzJywgJ0phY2tldHMnLCAnSmVhbnMnLCAnS25pdHdlYXInLCAnUG9sbyBTaGlydHMnLCAnU2hpcnRzJywgJ1Nob3J0cycsICdTdWl0cycsICdTd2VhdHNoaXJ0cycsICdULVNoaXJ0cycsICdUcm91c2VycyddXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBuYW1lOiAnU2hvZXMnLFxuICAgICAgICBpbWFnZTogJy9jYXRlZ29yeS1zaG9lcy5qcGcnLFxuICAgICAgICBpdGVtczogWydTaG9wIEFsbCcsICdCb290cycsICdEcmVzcyBTaG9lcycsICdMb2FmZXJzJywgJ1NhbmRhbHMnLCAnU25lYWtlcnMnLCAnVHJhaW5lcnMnXVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgbmFtZTogJ0JhZ3MnLFxuICAgICAgICBpbWFnZTogJy9jYXRlZ29yeS1iYWdzLmpwZycsXG4gICAgICAgIGl0ZW1zOiBbJ1Nob3AgQWxsJywgJ0JhY2twYWNrcycsICdCZWx0IEJhZ3MnLCAnQnJpZWZjYXNlcycsICdDcm9zcy1ib2R5JywgJ01lc3NlbmdlcicsICdUb3RlcycsICdUcmF2ZWwnXVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgbmFtZTogJ0FjY2Vzc29yaWVzJyxcbiAgICAgICAgaW1hZ2U6ICcvY2F0ZWdvcnktYWNjZXNzb3JpZXMuanBnJyxcbiAgICAgICAgaXRlbXM6IFsnU2hvcCBBbGwnLCAnQmVsdHMnLCAnR2xvdmVzJywgJ0hhdHMnLCAnSmV3ZWxyeSAmIFdhdGNoZXMnLCAnU2NhcnZlcycsICdTdW5nbGFzc2VzJywgJ1RlY2ggQWNjZXNzb3JpZXMnLCAnVGllcycsICdXYWxsZXRzJ11cbiAgICAgIH1cbiAgICBdLFxuICAgIGVkaXRvcmlhbDoge1xuICAgICAgdGl0bGU6ICdNT0RFUk4gQ0xBU1NJQ1MnLFxuICAgICAgc3VidGl0bGU6ICdUaW1lbGVzcyBwaWVjZXMgZm9yIHRoZSBtb2Rlcm4gbWFuJyxcbiAgICAgIGltYWdlOiAnL2VkaXRvcmlhbC0yLmpwZycsXG4gICAgICBsaW5rOiAnL21lbi9tb2Rlcm4tY2xhc3NpY3MnXG4gICAgfSxcbiAgICBkZXNpZ25lcnM6IFsnQWNuZSBTdHVkaW9zJywgJ0JvdHRlZ2EgVmVuZXRhJywgJ0ZlYXIgb2YgR29kJywgJ0phY3F1ZW11cycsICdLZW56bycsICdPZmYtV2hpdGUnLCAnU3RvbmUgSXNsYW5kJywgJ1Rob20gQnJvd25lJywgJ1RoZSBSb3cnLCAnVmFsZW50aW5vJ11cbiAgfVxufVxuXG4vLyBVdGlsaXR5IGZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IGZvcm1hdFByaWNlID0gKHByaWNlOiBzdHJpbmcgfCBudW1iZXIpOiBzdHJpbmcgPT4ge1xuICBpZiAodHlwZW9mIHByaWNlID09PSAnc3RyaW5nJykge1xuICAgIHJldHVybiBwcmljZS5zdGFydHNXaXRoKCfCoycpID8gcHJpY2UgOiBgwqMke3ByaWNlfWBcbiAgfVxuICByZXR1cm4gYMKjJHtwcmljZS50b0xvY2FsZVN0cmluZygpfWBcbn1cblxuZXhwb3J0IGNvbnN0IGdldFByb2R1Y3RzQnlDYXRlZ29yeSA9IChjYXRlZ29yeVNsdWc6IHN0cmluZyk6IFByb2R1Y3RbXSA9PiB7XG4gIC8vIEluIHByb2R1Y3Rpb24sIHRoaXMgd291bGQgZmlsdGVyIHByb2R1Y3RzIGZyb20gQVBJXG4gIHJldHVybiBtb2NrUHJvZHVjdHMuZmlsdGVyKHByb2R1Y3QgPT4gXG4gICAgcHJvZHVjdC5jYXRlZ29yaWVzPy5zb21lKGNhdCA9PiBjYXQuc2x1ZyA9PT0gY2F0ZWdvcnlTbHVnKVxuICApXG59XG5cbmV4cG9ydCBjb25zdCBnZXRQcm9kdWN0QnlJZCA9IChpZDogc3RyaW5nKTogUHJvZHVjdCB8IHVuZGVmaW5lZCA9PiB7XG4gIHJldHVybiBtb2NrUHJvZHVjdHMuZmluZChwcm9kdWN0ID0+IHByb2R1Y3QuaWQgPT09IGlkKVxufVxuXG5leHBvcnQgY29uc3Qgc2VhcmNoUHJvZHVjdHMgPSAocXVlcnk6IHN0cmluZyk6IFByb2R1Y3RbXSA9PiB7XG4gIGNvbnN0IGxvd2VyY2FzZVF1ZXJ5ID0gcXVlcnkudG9Mb3dlckNhc2UoKVxuICByZXR1cm4gbW9ja1Byb2R1Y3RzLmZpbHRlcihwcm9kdWN0ID0+XG4gICAgcHJvZHVjdC5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJjYXNlUXVlcnkpIHx8XG4gICAgcHJvZHVjdC5icmFuZC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyY2FzZVF1ZXJ5KVxuICApXG59XG4iXSwibmFtZXMiOlsibW9ja1Byb2R1Y3RzIiwiaWQiLCJuYW1lIiwiYnJhbmQiLCJwcmljZSIsImltYWdlIiwiaXNPblNhbGUiLCJjb2xvcnMiLCJzaXplcyIsIm9yaWdpbmFsUHJpY2UiLCJjYXRlZ29yaWVzIiwic2x1ZyIsImNvdW50IiwiaHJlZiIsImZpbHRlck9wdGlvbnMiLCJvcHRpb25zIiwibWVnYU1lbnVEYXRhIiwid29tZW4iLCJmZWF0dXJlZCIsInRpdGxlIiwic3VidGl0bGUiLCJsaW5rIiwiaXRlbXMiLCJlZGl0b3JpYWwiLCJkZXNpZ25lcnMiLCJtZW4iLCJmb3JtYXRQcmljZSIsInN0YXJ0c1dpdGgiLCJ0b0xvY2FsZVN0cmluZyIsImdldFByb2R1Y3RzQnlDYXRlZ29yeSIsImNhdGVnb3J5U2x1ZyIsImZpbHRlciIsInByb2R1Y3QiLCJzb21lIiwiY2F0IiwiZ2V0UHJvZHVjdEJ5SWQiLCJmaW5kIiwic2VhcmNoUHJvZHVjdHMiLCJxdWVyeSIsImxvd2VyY2FzZVF1ZXJ5IiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/data.ts\n");

/***/ }),

/***/ "(ssr)/./lib/hooks.ts":
/*!**********************!*\
  !*** ./lib/hooks.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useClickOutside: () => (/* binding */ useClickOutside),\n/* harmony export */   useDebounce: () => (/* binding */ useDebounce),\n/* harmony export */   useHoverDelay: () => (/* binding */ useHoverDelay),\n/* harmony export */   useIsMobile: () => (/* binding */ useIsMobile),\n/* harmony export */   useLocalStorage: () => (/* binding */ useLocalStorage),\n/* harmony export */   useProductFilters: () => (/* binding */ useProductFilters)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n// Custom hooks for the Matches Headless UI\n\n// Hook for managing mobile detection\nfunction useIsMobile(breakpoint = 768) {\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < breakpoint);\n        };\n        checkMobile();\n        window.addEventListener('resize', checkMobile);\n        return ()=>window.removeEventListener('resize', checkMobile);\n    }, [\n        breakpoint\n    ]);\n    return isMobile;\n}\n// Hook for managing outside clicks\nfunction useClickOutside(ref, callback) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (ref.current && !ref.current.contains(event.target)) {\n                callback();\n            }\n        };\n        document.addEventListener('mousedown', handleClickOutside);\n        return ()=>document.removeEventListener('mousedown', handleClickOutside);\n    }, [\n        ref,\n        callback\n    ]);\n}\n// Hook for managing hover delays (useful for mega menus)\nfunction useHoverDelay() {\n    const [timeoutRef, setTimeoutRef] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const setHoverTimeout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback, delay)=>{\n        if (timeoutRef) {\n            clearTimeout(timeoutRef);\n        }\n        const newTimeout = setTimeout(callback, delay);\n        setTimeoutRef(newTimeout);\n    }, [\n        timeoutRef\n    ]);\n    const clearHoverTimeout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (timeoutRef) {\n            clearTimeout(timeoutRef);\n            setTimeoutRef(null);\n        }\n    }, [\n        timeoutRef\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (timeoutRef) {\n                clearTimeout(timeoutRef);\n            }\n        };\n    }, [\n        timeoutRef\n    ]);\n    return {\n        setHoverTimeout,\n        clearHoverTimeout\n    };\n}\n// Hook for managing product filters\nfunction useProductFilters(products) {\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(products);\n    const updateFilter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    }, []);\n    const clearFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setFilters({});\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        let filtered = [\n            ...products\n        ];\n        // Apply category filter\n        if (filters.category) {\n            filtered = filtered.filter((product)=>product.categories?.some((cat)=>cat.slug === filters.category));\n        }\n        // Apply brand filter\n        if (filters.brand) {\n            filtered = filtered.filter((product)=>product.brand.toLowerCase() === filters.brand?.toLowerCase());\n        }\n        // Apply color filters\n        if (filters.colors && filters.colors.length > 0) {\n            filtered = filtered.filter((product)=>product.colors?.some((color)=>filters.colors?.includes(typeof color === 'string' ? color : color.name)));\n        }\n        // Apply size filters\n        if (filters.sizes && filters.sizes.length > 0) {\n            filtered = filtered.filter((product)=>product.sizes?.some((size)=>filters.sizes?.includes(size)));\n        }\n        // Apply price range filter\n        if (filters.priceRange) {\n            const [min, max] = filters.priceRange;\n            filtered = filtered.filter((product)=>{\n                const price = typeof product.price === 'string' ? parseFloat(product.price.replace(/[£,]/g, '')) : product.price;\n                return price >= min && price <= max;\n            });\n        }\n        // Apply sorting\n        if (filters.sortBy) {\n            switch(filters.sortBy){\n                case 'price-low':\n                    filtered.sort((a, b)=>{\n                        const priceA = typeof a.price === 'string' ? parseFloat(a.price.replace(/[£,]/g, '')) : a.price;\n                        const priceB = typeof b.price === 'string' ? parseFloat(b.price.replace(/[£,]/g, '')) : b.price;\n                        return priceA - priceB;\n                    });\n                    break;\n                case 'price-high':\n                    filtered.sort((a, b)=>{\n                        const priceA = typeof a.price === 'string' ? parseFloat(a.price.replace(/[£,]/g, '')) : a.price;\n                        const priceB = typeof b.price === 'string' ? parseFloat(b.price.replace(/[£,]/g, '')) : b.price;\n                        return priceB - priceA;\n                    });\n                    break;\n                case 'newest':\n                    filtered.sort((a, b)=>{\n                        const dateA = new Date(a.date_created || '').getTime();\n                        const dateB = new Date(b.date_created || '').getTime();\n                        return dateB - dateA;\n                    });\n                    break;\n                case 'popular':\n                    // Sort by featured first, then by some popularity metric\n                    filtered.sort((a, b)=>{\n                        if (a.featured && !b.featured) return -1;\n                        if (!a.featured && b.featured) return 1;\n                        return 0;\n                    });\n                    break;\n            }\n        }\n        setFilteredProducts(filtered);\n    }, [\n        products,\n        filters\n    ]);\n    return {\n        filters,\n        filteredProducts,\n        updateFilter,\n        clearFilters,\n        hasActiveFilters: Object.keys(filters).length > 0\n    };\n}\n// Hook for managing local storage\nfunction useLocalStorage(key, initialValue) {\n    const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{\n        try {\n            if (false) {}\n            return initialValue;\n        } catch (error) {\n            console.error(`Error reading localStorage key \"${key}\":`, error);\n            return initialValue;\n        }\n    });\n    const setValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((value)=>{\n        try {\n            const valueToStore = value instanceof Function ? value(storedValue) : value;\n            setStoredValue(valueToStore);\n            if (false) {}\n        } catch (error) {\n            console.error(`Error setting localStorage key \"${key}\":`, error);\n        }\n    }, [\n        key,\n        storedValue\n    ]);\n    return [\n        storedValue,\n        setValue\n    ];\n}\n// Hook for debouncing values (useful for search)\nfunction useDebounce(value, delay) {\n    const [debouncedValue, setDebouncedValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handler = setTimeout(()=>{\n            setDebouncedValue(value);\n        }, delay);\n        return ()=>{\n            clearTimeout(handler);\n        };\n    }, [\n        value,\n        delay\n    ]);\n    return debouncedValue;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/hooks.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1ff10bf8f9ab\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXRjaGVzLWhlYWRsZXNzLy4vYXBwL2dsb2JhbHMuY3NzP2YwZjEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxZmYxMGJmOGY5YWJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n/* harmony import */ var _components_SaleBanner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SaleBanner */ \"(rsc)/./components/SaleBanner.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./components/ErrorBoundary.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Header__WEBPACK_IMPORTED_MODULE_2__, _components_SaleBanner__WEBPACK_IMPORTED_MODULE_4__, _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_5__]);\n([_components_Header__WEBPACK_IMPORTED_MODULE_2__, _components_SaleBanner__WEBPACK_IMPORTED_MODULE_4__, _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst metadata = {\n    title: 'Matches Headless',\n    description: 'Headless Matchesfashion style storefront'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen flex flex-col bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SaleBanner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\layout.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\layout.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/data */ \"(rsc)/./lib/data.ts\");\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gray-50 min-h-[90vh] flex items-center py-32 lg:py-48 xl:py-56\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-24 lg:gap-32 xl:gap-40 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8 lg:space-y-10 xl:space-y-12 text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-light text-black leading-tight tracking-tight\",\n                                        children: [\n                                            \"NEW SEASON\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 14,\n                                                columnNumber: 27\n                                            }, this),\n                                            \"HEROES\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                        lineNumber: 13,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg lg:text-xl xl:text-2xl text-gray-600 max-w-lg xl:max-w-xl leading-relaxed mx-auto lg:mx-0\",\n                                        children: \"Discover the latest arrivals from your favorite designers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 lg:gap-6 justify-center lg:justify-start pt-4 lg:pt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/women\",\n                                                className: \"bg-black text-white px-8 py-3 text-sm font-medium tracking-wide hover:bg-gray-800 transition-colors text-center min-w-[140px]\",\n                                                children: \"SHOP WOMEN\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 20,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/men\",\n                                                className: \"border-2 border-black text-black px-8 py-3 text-sm font-medium tracking-wide hover:bg-black hover:text-white transition-colors text-center min-w-[140px]\",\n                                                children: \"SHOP MEN\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-[400px] lg:h-[500px] xl:h-[600px] w-full max-w-md lg:max-w-none mx-auto lg:mx-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"/hero-image.jpg\",\n                                    alt: \"New Season Heroes\",\n                                    fill: true,\n                                    className: \"object-cover\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-40 lg:py-56 xl:py-64 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-28 lg:mb-36 xl:mb-44\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl lg:text-5xl xl:text-6xl font-light text-black mb-12 lg:mb-16 tracking-wide\",\n                                    children: \"SHOP BY CATEGORY\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-32 lg:w-40 h-px bg-black mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-12 sm:gap-16 lg:gap-20 xl:gap-24\",\n                            children: _lib_data__WEBPACK_IMPORTED_MODULE_3__.categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: category.href || `/${category.slug}`,\n                                    className: \"group text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative aspect-[4/5] mb-10 lg:mb-14 xl:mb-16 overflow-hidden bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: category.image,\n                                                alt: category.name,\n                                                fill: true,\n                                                className: \"object-cover group-hover:scale-105 transition-transform duration-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4 lg:space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-base lg:text-lg xl:text-xl font-semibold text-black tracking-wide\",\n                                                    children: category.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm lg:text-base text-gray-500 uppercase tracking-wide\",\n                                                    children: category.count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, category.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-40 lg:py-56 xl:py-64 bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-24 lg:gap-32 xl:gap-40\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative aspect-[3/4] group overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/editorial-1.jpg\",\n                                        alt: \"Editorial Content\",\n                                        fill: true,\n                                        className: \"object-cover group-hover:scale-105 transition-transform duration-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-10 lg:bottom-12 xl:bottom-16 left-10 lg:left-12 xl:left-16 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl lg:text-3xl xl:text-4xl font-light mb-4 lg:mb-6 tracking-wide\",\n                                                children: \"WINTER ESSENTIALS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-base lg:text-lg mb-8 lg:mb-10 opacity-90\",\n                                                children: \"Discover our curated edit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/winter-essentials\",\n                                                className: \"text-sm lg:text-base font-medium tracking-wide border-b border-white pb-1 hover:border-opacity-70 transition-all\",\n                                                children: \"SHOP NOW\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8 lg:space-y-10 xl:space-y-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative aspect-[4/3] group overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: \"/editorial-2.jpg\",\n                                                alt: \"Editorial Content\",\n                                                fill: true,\n                                                className: \"object-cover group-hover:scale-105 transition-transform duration-700\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-8 lg:bottom-10 xl:bottom-12 left-8 lg:left-10 xl:left-12 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl lg:text-2xl xl:text-3xl font-light mb-4 lg:mb-6 tracking-wide\",\n                                                        children: \"THE GOLDEN HOUR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: \"/golden-hour\",\n                                                        className: \"text-sm lg:text-base font-medium tracking-wide border-b border-white pb-1 hover:border-opacity-70 transition-all\",\n                                                        children: \"DISCOVER\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-6 lg:gap-8 xl:gap-10\",\n                                        children: [\n                                            {\n                                                name: 'DRESSES',\n                                                image: '/mini-1.jpg'\n                                            },\n                                            {\n                                                name: 'KNITWEAR',\n                                                image: '/mini-2.jpg'\n                                            },\n                                            {\n                                                name: 'OUTERWEAR',\n                                                image: '/mini-3.jpg'\n                                            }\n                                        ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: `/${item.name.toLowerCase()}`,\n                                                className: \"group text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative aspect-[4/5] mb-4 lg:mb-6 xl:mb-8 overflow-hidden bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: item.image,\n                                                            alt: item.name,\n                                                            fill: true,\n                                                            className: \"object-cover group-hover:scale-105 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs lg:text-sm font-semibold tracking-wide text-black\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-40 lg:py-56 xl:py-64 bg-black text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl lg:text-5xl xl:text-6xl font-light mb-16 lg:mb-20 xl:mb-24 tracking-wide\",\n                                children: \"SIGN UP TO OUR EMAILS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 mb-20 lg:mb-28 xl:mb-32 text-xl lg:text-2xl leading-relaxed\",\n                                children: \"Be the first to know about new arrivals, exclusive offers and style inspiration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-8 sm:gap-12 max-w-3xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        placeholder: \"Enter your email address\",\n                                        className: \"flex-1 px-4 sm:px-6 py-3 sm:py-4 bg-transparent border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:border-white text-base rounded-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-white text-black px-6 sm:px-8 py-3 sm:py-4 font-semibold tracking-wide hover:bg-gray-100 transition-colors text-sm whitespace-nowrap\",\n                                        children: \"SIGN UP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ErrorBoundary.tsx":
/*!**************************************!*\
  !*** ./components/ErrorBoundary.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProductErrorFallback: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useErrorHandler: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const proxy = await (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\components\ErrorBoundary.tsx`)
const e0 = proxy["ProductErrorFallback"];

const e1 = proxy["useErrorHandler"];

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (proxy.default);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } }, 1);

/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-50 border-t border-gray-200 text-gray-600 text-sm py-24 lg:py-32 xl:py-40 mt-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-12 lg:gap-16 xl:gap-20 mb-20 lg:mb-28 xl:mb-32\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-4 lg:mb-6 xl:mb-8 text-black text-sm uppercase tracking-wide\",\n                                    children: \"MATCHES\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 9,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3 lg:space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 11,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 11,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Careers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 12,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 12,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Affiliates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 13,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 13,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Press\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 14,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 14,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 10,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-4 lg:mb-6 xl:mb-8 text-black text-sm uppercase tracking-wide\",\n                                    children: \"Customer Care\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3 lg:space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Contact Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Size Guide\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Delivery\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Returns\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 25,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-4 lg:mb-6 xl:mb-8 text-black text-sm uppercase tracking-wide\",\n                                    children: \"Services\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3 lg:space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Personal Shopping\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Gift Cards\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Loyalty Program\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Style Advice\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-4 lg:mb-6 xl:mb-8 text-black text-sm uppercase tracking-wide\",\n                                    children: \"Legal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3 lg:space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Terms & Conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Cookie Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-4 lg:mb-6 xl:mb-8 text-black text-sm uppercase tracking-wide\",\n                                    children: \"Connect\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3 lg:space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Instagram\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Twitter\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Facebook\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-black transition-colors text-sm\",\n                                                children: \"Pinterest\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-200 pt-8 lg:pt-10 xl:pt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row justify-between items-center gap-6 lg:gap-8 xl:gap-10 text-center lg:text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 order-1 lg:order-1\",\n                                children: \"\\xa9 2024 MATCHES. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 order-3 lg:order-2\",\n                                children: \"Shipping worldwide | Customer service: +44 (0)20 7647 8888\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row items-center gap-3 sm:gap-6 order-2 lg:order-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-500 whitespace-nowrap\",\n                                        children: \"Download our app:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 sm:gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"bg-black text-white px-3 sm:px-4 py-2 text-xs hover:bg-gray-800 transition-colors\",\n                                                children: \"App Store\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"bg-black text-white px-3 sm:px-4 py-2 text-xs hover:bg-gray-800 transition-colors\",\n                                                children: \"Google Play\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n            lineNumber: 5,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\Footer.tsx\",\n        lineNumber: 4,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const proxy = await (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\components\Header.tsx`)
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (proxy.default);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } }, 1);

/***/ }),

/***/ "(rsc)/./components/SaleBanner.tsx":
/*!***********************************!*\
  !*** ./components/SaleBanner.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const proxy = await (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\components\SaleBanner.tsx`)
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (proxy.default);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } }, 1);

/***/ }),

/***/ "(rsc)/./lib/data.ts":
/*!*********************!*\
  !*** ./lib/data.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categories: () => (/* binding */ categories),\n/* harmony export */   filterOptions: () => (/* binding */ filterOptions),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductsByCategory: () => (/* binding */ getProductsByCategory),\n/* harmony export */   megaMenuData: () => (/* binding */ megaMenuData),\n/* harmony export */   mockProducts: () => (/* binding */ mockProducts),\n/* harmony export */   searchProducts: () => (/* binding */ searchProducts)\n/* harmony export */ });\n// Centralized data management for the Matches Headless UI\n// Mock product data - in production this would come from WooCommerce API\nconst mockProducts = [\n    {\n        id: '1',\n        name: 'Moon oversized cotton-poplin shirt',\n        brand: 'The Row',\n        price: '£1,200',\n        image: '/products/the-row-shirt.jpg',\n        isOnSale: false,\n        colors: [\n            'White',\n            'Black',\n            'Navy'\n        ],\n        sizes: [\n            'XS',\n            'S',\n            'M',\n            'L',\n            'XL'\n        ]\n    },\n    {\n        id: '2',\n        name: 'Cashmere sweater',\n        brand: 'Brunello Cucinelli',\n        price: '£890',\n        image: '/products/brunello-sweater.jpg',\n        isOnSale: false,\n        colors: [\n            'Beige',\n            'Grey',\n            'Navy'\n        ]\n    },\n    {\n        id: '3',\n        name: 'Wool coat',\n        brand: 'Max Mara',\n        price: '£1,450',\n        originalPrice: '£1,800',\n        image: '/products/maxmara-coat.jpg',\n        isOnSale: true,\n        colors: [\n            'Camel',\n            'Black',\n            'Navy'\n        ]\n    },\n    {\n        id: '4',\n        name: 'Silk dress',\n        brand: 'Bottega Veneta',\n        price: '£2,100',\n        image: '/products/bottega-dress.jpg',\n        isOnSale: false,\n        colors: [\n            'Black',\n            'Green',\n            'Brown'\n        ]\n    },\n    {\n        id: '5',\n        name: 'Leather jacket',\n        brand: 'Saint Laurent',\n        price: '£3,200',\n        image: '/products/saint-laurent-jacket.jpg',\n        isOnSale: false,\n        colors: [\n            'Black'\n        ]\n    },\n    {\n        id: '6',\n        name: 'Knit sweater',\n        brand: 'Ganni',\n        price: '£295',\n        image: '/products/ganni-sweater.jpg',\n        isOnSale: false,\n        colors: [\n            'Pink',\n            'Blue',\n            'Yellow'\n        ]\n    }\n];\n// Category data\nconst categories = [\n    {\n        id: 1,\n        name: 'CLOTHING',\n        slug: 'clothing',\n        count: '545 PRODUCTS',\n        image: '/category-clothing.jpg',\n        href: '/clothing'\n    },\n    {\n        id: 2,\n        name: 'SHOES',\n        slug: 'shoes',\n        count: '234 PRODUCTS',\n        image: '/category-shoes.jpg',\n        href: '/shoes'\n    },\n    {\n        id: 3,\n        name: 'BAGS',\n        slug: 'bags',\n        count: '156 PRODUCTS',\n        image: '/category-bags.jpg',\n        href: '/bags'\n    },\n    {\n        id: 4,\n        name: 'ACCESSORIES',\n        slug: 'accessories',\n        count: '89 PRODUCTS',\n        image: '/category-accessories.jpg',\n        href: '/accessories'\n    },\n    {\n        id: 5,\n        name: 'SALE',\n        slug: 'sale',\n        count: 'UP TO 70% OFF',\n        image: '/category-sale.jpg',\n        href: '/sale'\n    }\n];\n// Filter options\nconst filterOptions = [\n    {\n        name: 'Categories',\n        options: [\n            'Clothing',\n            'Shoes',\n            'Bags',\n            'Accessories'\n        ]\n    },\n    {\n        name: 'Designer',\n        options: [\n            'Bottega Veneta',\n            'Gucci',\n            'Prada',\n            'Saint Laurent',\n            'The Row',\n            'Brunello Cucinelli',\n            'Max Mara',\n            'Ganni'\n        ]\n    },\n    {\n        name: 'Colour',\n        options: [\n            'Black',\n            'White',\n            'Brown',\n            'Blue',\n            'Green',\n            'Red',\n            'Pink',\n            'Yellow',\n            'Beige',\n            'Grey',\n            'Navy',\n            'Camel'\n        ]\n    },\n    {\n        name: 'Clothes Size',\n        options: [\n            'XXS',\n            'XS',\n            'S',\n            'M',\n            'L',\n            'XL',\n            'XXL'\n        ]\n    },\n    {\n        name: 'Shoe Sizes',\n        options: [\n            '5',\n            '6',\n            '7',\n            '8',\n            '9',\n            '10',\n            '11'\n        ]\n    },\n    {\n        name: 'Shop By',\n        options: [\n            'New Arrivals',\n            'Sale',\n            'Just In',\n            'Trending'\n        ]\n    },\n    {\n        name: 'Price',\n        options: [\n            'Under £100',\n            '£100 - £500',\n            '£500 - £1000',\n            'Over £1000'\n        ]\n    }\n];\n// Mega menu data\nconst megaMenuData = {\n    women: {\n        featured: {\n            title: 'NEW SEASON ARRIVALS',\n            subtitle: 'Discover the latest from your favorite designers',\n            image: '/hero-image.jpg',\n            link: '/women/new-arrivals'\n        },\n        categories: [\n            {\n                name: 'Clothing',\n                image: '/category-clothing.jpg',\n                items: [\n                    'Shop All',\n                    'Dresses',\n                    'Knitwear',\n                    'Coats',\n                    'Jackets',\n                    'Jeans',\n                    'Jumpsuits',\n                    'Lingerie',\n                    'Skirts',\n                    'Suits',\n                    'Swimwear',\n                    'Tops',\n                    'Trousers'\n                ]\n            },\n            {\n                name: 'Shoes',\n                image: '/category-shoes.jpg',\n                items: [\n                    'Shop All',\n                    'Boots',\n                    'Flats',\n                    'Heels',\n                    'Sandals',\n                    'Sneakers',\n                    'Trainers'\n                ]\n            },\n            {\n                name: 'Bags',\n                image: '/category-bags.jpg',\n                items: [\n                    'Shop All',\n                    'Backpacks',\n                    'Belt Bags',\n                    'Clutches',\n                    'Cross-body',\n                    'Handbags',\n                    'Shoulder Bags',\n                    'Totes',\n                    'Travel'\n                ]\n            },\n            {\n                name: 'Accessories',\n                image: '/category-accessories.jpg',\n                items: [\n                    'Shop All',\n                    'Belts',\n                    'Gloves',\n                    'Hair Accessories',\n                    'Hats',\n                    'Jewelry & Watches',\n                    'Scarves',\n                    'Sunglasses',\n                    'Tech Accessories'\n                ]\n            }\n        ],\n        editorial: {\n            title: 'WINTER ESSENTIALS',\n            subtitle: 'Curated edit for the season',\n            image: '/editorial-1.jpg',\n            link: '/women/winter-essentials'\n        },\n        designers: [\n            'Acne Studios',\n            'Bottega Veneta',\n            'Ganni',\n            'Jacquemus',\n            'Khaite',\n            'Loewe',\n            'Prada',\n            'Saint Laurent',\n            'The Row',\n            'Toteme'\n        ]\n    },\n    men: {\n        featured: {\n            title: 'NEW SEASON ARRIVALS',\n            subtitle: 'Discover the latest from your favorite designers',\n            image: '/hero-image.jpg',\n            link: '/men/new-arrivals'\n        },\n        categories: [\n            {\n                name: 'Clothing',\n                image: '/category-clothing.jpg',\n                items: [\n                    'Shop All',\n                    'Blazers',\n                    'Coats',\n                    'Hoodies',\n                    'Jackets',\n                    'Jeans',\n                    'Knitwear',\n                    'Polo Shirts',\n                    'Shirts',\n                    'Shorts',\n                    'Suits',\n                    'Sweatshirts',\n                    'T-Shirts',\n                    'Trousers'\n                ]\n            },\n            {\n                name: 'Shoes',\n                image: '/category-shoes.jpg',\n                items: [\n                    'Shop All',\n                    'Boots',\n                    'Dress Shoes',\n                    'Loafers',\n                    'Sandals',\n                    'Sneakers',\n                    'Trainers'\n                ]\n            },\n            {\n                name: 'Bags',\n                image: '/category-bags.jpg',\n                items: [\n                    'Shop All',\n                    'Backpacks',\n                    'Belt Bags',\n                    'Briefcases',\n                    'Cross-body',\n                    'Messenger',\n                    'Totes',\n                    'Travel'\n                ]\n            },\n            {\n                name: 'Accessories',\n                image: '/category-accessories.jpg',\n                items: [\n                    'Shop All',\n                    'Belts',\n                    'Gloves',\n                    'Hats',\n                    'Jewelry & Watches',\n                    'Scarves',\n                    'Sunglasses',\n                    'Tech Accessories',\n                    'Ties',\n                    'Wallets'\n                ]\n            }\n        ],\n        editorial: {\n            title: 'MODERN CLASSICS',\n            subtitle: 'Timeless pieces for the modern man',\n            image: '/editorial-2.jpg',\n            link: '/men/modern-classics'\n        },\n        designers: [\n            'Acne Studios',\n            'Bottega Veneta',\n            'Fear of God',\n            'Jacquemus',\n            'Kenzo',\n            'Off-White',\n            'Stone Island',\n            'Thom Browne',\n            'The Row',\n            'Valentino'\n        ]\n    }\n};\n// Utility functions\nconst formatPrice = (price)=>{\n    if (typeof price === 'string') {\n        return price.startsWith('£') ? price : `£${price}`;\n    }\n    return `£${price.toLocaleString()}`;\n};\nconst getProductsByCategory = (categorySlug)=>{\n    // In production, this would filter products from API\n    return mockProducts.filter((product)=>product.categories?.some((cat)=>cat.slug === categorySlug));\n};\nconst getProductById = (id)=>{\n    return mockProducts.find((product)=>product.id === id);\n};\nconst searchProducts = (query)=>{\n    const lowercaseQuery = query.toLowerCase();\n    return mockProducts.filter((product)=>product.name.toLowerCase().includes(lowercaseQuery) || product.brand.toLowerCase().includes(lowercaseQuery));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/data.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@heroicons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CCharles%20Bartowski%5CDownloads%5Cmatches-headless-ui%5Cmatches-headless-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCharles%20Bartowski%5CDownloads%5Cmatches-headless-ui%5Cmatches-headless-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();