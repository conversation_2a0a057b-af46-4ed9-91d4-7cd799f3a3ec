// Shared type definitions for the Matches Headless UI

export interface Product {
  id: string | number
  name: string
  brand: string
  price: string | number
  originalPrice?: string | number
  image: string
  images?: string[]
  isOnSale?: boolean
  colors?: string[] | Color[]
  sizes?: string[]
  description?: string
  details?: string[]
  sku?: string
  stock_status?: 'instock' | 'outofstock' | 'onbackorder'
  featured?: boolean
  date_created?: string
  categories?: Category[]
  tags?: Tag[]
  attributes?: Attribute[]
}

export interface Color {
  name: string
  value: string
}

export interface Category {
  id: number
  name: string
  slug: string
  image?: string
  href?: string
  count?: string
}

export interface Tag {
  id: number
  name: string
  slug: string
}

export interface Attribute {
  id: number
  name: string
  options: string[]
}

export interface MegaMenuCategory {
  name: string
  image: string
  items: string[]
}

export interface MegaMenuData {
  featured: {
    title: string
    subtitle: string
    image: string
    link: string
  }
  categories: MegaMenuCategory[]
  editorial: {
    title: string
    subtitle: string
    image: string
    link: string
  }
  designers: string[]
}

export interface FilterOption {
  name: string
  options: string[]
}

export interface ProductFiltersProps {
  isOpen: boolean
  onClose: () => void
}

export interface ProductCardProps {
  product: Product
}

// WooCommerce specific types
export interface WooCommerceProduct {
  id: number
  name: string
  price: string
  regular_price: string
  sale_price: string
  on_sale: boolean
  images: { src: string; alt?: string }[]
  categories: Category[]
  tags: Tag[]
  short_description: string
  description: string
  sku: string
  stock_status: 'instock' | 'outofstock' | 'onbackorder'
  featured: boolean
  date_created: string
  attributes: Attribute[]
}

// Navigation types
export interface NavItem {
  name: string
  href: string
  megaMenu?: MegaMenuData
}

// API Response types
export interface ApiResponse<T> {
  data: T
  error?: string
  loading?: boolean
}

// Form types
export interface ContactForm {
  name: string
  email: string
  message: string
}

export interface NewsletterForm {
  email: string
}

// Cart types
export interface CartItem {
  id: string
  product: Product
  quantity: number
  selectedSize?: string
  selectedColor?: Color
}

export interface Cart {
  items: CartItem[]
  total: number
  itemCount: number
}

// User types
export interface User {
  id: string
  email: string
  name?: string
  avatar?: string
}

// Search types
export interface SearchFilters {
  category?: string
  brand?: string
  priceRange?: [number, number]
  colors?: string[]
  sizes?: string[]
  sortBy?: 'newest' | 'price-low' | 'price-high' | 'popular'
}

export interface SearchResults {
  products: Product[]
  total: number
  page: number
  hasMore: boolean
}
