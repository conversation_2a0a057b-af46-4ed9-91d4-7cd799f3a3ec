'use client'

import Image from 'next/image'
import Link from 'next/link'

const quickCategories = [
  {
    id: 'women',
    name: 'Women',
    image: '/carousel/women.svg',
    href: '/women'
  },
  {
    id: 'men',
    name: 'Men',
    image: '/carousel/men.svg',
    href: '/men'
  },
  {
    id: 'bags',
    name: 'Bags',
    image: '/carousel/bags.svg',
    href: '/bags'
  },
  {
    id: 'shoes',
    name: 'Shoes',
    image: '/carousel/shoes.svg',
    href: '/shoes'
  }
]

export default function CategoryCarousel() {
  return (
    <div className="mt-8">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
        {quickCategories.map((category) => (
          <Link
            key={category.id}
            href={category.href}
            className="group block text-center transition-transform hover:-translate-y-1"
          >
            <div className="relative aspect-square bg-gray-100 overflow-hidden mb-4">
              <Image
                src={category.image}
                alt={category.name}
                fill
                sizes="(max-width: 768px) 160px, 200px"
                className="object-cover transition-transform duration-300 group-hover:scale-105"
              />
            </div>
            <h3 className="text-base font-medium text-black uppercase tracking-wide">{category.name}</h3>
          </Link>
        ))}
      </div>
    </div>
  )
}