'use client'

import { useState } from 'react'

export default function NewsletterSignup() {
  const [email, setEmail] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (email) {
      // In production, this would call an API to subscribe the user
      console.log('Newsletter signup:', email)
      setIsSubmitted(true)
      setEmail('')
      
      // Reset after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false)
      }, 3000)
    }
  }

  return (
    <section className="bg-white py-16 lg:py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-2xl lg:text-3xl font-light mb-4 lg:mb-6 tracking-wide text-black">
            Sign up
          </h2>
          <p className="text-gray-600 mb-8 lg:mb-12 text-sm lg:text-base">
            Be the first to know about new arrivals, exclusive offers and style inspiration
          </p>
          
          {isSubmitted ? (
            <div className="bg-green-50 border border-green-200 rounded-none px-6 py-4 text-green-800 text-sm">
              Thank you for subscribing! You'll receive our latest updates soon.
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                className="flex-1 px-4 py-3 border border-gray-300 bg-white text-black placeholder-gray-500 focus:outline-none focus:border-black text-sm"
                required
              />
              <button
                type="submit"
                className="bg-black text-white px-6 py-3 text-sm font-medium tracking-wider hover:bg-gray-800 transition-colors whitespace-nowrap"
              >
                SIGN UP
              </button>
            </form>
          )}
        </div>
      </div>
    </section>
  )
}
