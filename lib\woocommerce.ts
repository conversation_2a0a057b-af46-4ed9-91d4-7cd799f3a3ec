import axios, { AxiosError } from 'axios'
import { WooCommerceProduct } from './types'

// Create axios instance with better error handling
const createWooCommerceClient = () => {
  const baseURL = process.env.WOOCOMMERCE_SITE_URL
  const consumerKey = process.env.WOOCOMMERCE_CONSUMER_KEY
  const consumerSecret = process.env.WOOCOMMERCE_CONSUMER_SECRET

  if (!baseURL || !consumerKey || !consumerSecret) {
    console.warn('WooCommerce credentials not configured. Using mock data.')
    return null
  }

  return axios.create({
    baseURL: `${baseURL}/wp-json/wc/v3`,
    auth: {
      username: consumerKey,
      password: consumerSecret
    },
    timeout: 10000, // 10 second timeout
    headers: {
      'Content-Type': 'application/json',
    }
  })
}

export const woo = createWooCommerceClient()

// Enhanced error handling
export class WooCommerceError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: AxiosError
  ) {
    super(message)
    this.name = 'WooCommerceError'
  }
}

// Fetch products with error handling and fallback
export async function fetchProducts(params: Record<string, string | number> = {}): Promise<WooCommerceProduct[]> {
  if (!woo) {
    console.warn('WooCommerce not configured, returning empty array')
    return []
  }

  try {
    const { data } = await woo.get<WooCommerceProduct[]>('/products', {
      params: {
        per_page: 20,
        status: 'publish',
        ...params
      }
    })
    return data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const statusCode = error.response?.status
      const message = error.response?.data?.message || error.message

      console.error('WooCommerce API Error:', {
        statusCode,
        message,
        url: error.config?.url
      })

      throw new WooCommerceError(
        `Failed to fetch products: ${message}`,
        statusCode,
        error
      )
    }

    console.error('Unexpected error fetching products:', error)
    throw new WooCommerceError('An unexpected error occurred while fetching products')
  }
}

// Fetch single product
export async function fetchProduct(id: string | number): Promise<WooCommerceProduct | null> {
  if (!woo) {
    console.warn('WooCommerce not configured')
    return null
  }

  try {
    const { data } = await woo.get<WooCommerceProduct>(`/products/${id}`)
    return data
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      return null
    }

    console.error('Error fetching product:', error)
    throw new WooCommerceError(`Failed to fetch product ${id}`)
  }
}

// Fetch categories
export async function fetchCategories(params: Record<string, string | number> = {}) {
  if (!woo) {
    console.warn('WooCommerce not configured')
    return []
  }

  try {
    const { data } = await woo.get('/products/categories', {
      params: {
        per_page: 50,
        hide_empty: true,
        ...params
      }
    })
    return data
  } catch (error) {
    console.error('Error fetching categories:', error)
    throw new WooCommerceError('Failed to fetch categories')
  }
}

// Search products
export async function searchProducts(query: string, params: Record<string, string | number> = {}) {
  if (!woo) {
    console.warn('WooCommerce not configured')
    return []
  }

  try {
    const { data } = await woo.get('/products', {
      params: {
        search: query,
        per_page: 20,
        status: 'publish',
        ...params
      }
    })
    return data
  } catch (error) {
    console.error('Error searching products:', error)
    throw new WooCommerceError(`Failed to search products for "${query}"`)
  }
}

// Health check for WooCommerce API
export async function checkWooCommerceHealth(): Promise<boolean> {
  if (!woo) {
    return false
  }

  try {
    await woo.get('/system_status')
    return true
  } catch (error) {
    console.error('WooCommerce health check failed:', error)
    return false
  }
}
