// Loading components for the Matches Headless UI

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

// Main loading spinner
export function LoadingSpinner({ size = 'md', className = '' }: LoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  }

  return (
    <div className={`animate-spin ${sizeClasses[size]} ${className}`}>
      <svg
        className="h-full w-full text-gray-400"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>
  )
}

// Page loading component
export function PageLoading() {
  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner size="lg" className="mx-auto mb-4" />
        <p className="text-gray-600 text-sm">Loading...</p>
      </div>
    </div>
  )
}

// Product card skeleton
export function ProductCardSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="aspect-[3/4] bg-gray-200 mb-4"></div>
      <div className="space-y-2">
        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        <div className="h-3 bg-gray-200 rounded w-1/3"></div>
      </div>
    </div>
  )
}

// Product grid skeleton
export function ProductGridSkeleton({ count = 6 }: { count?: number }) {
  return (
    <div className="grid grid-cols-2 lg:grid-cols-3 gap-12 lg:gap-16 xl:gap-20">
      {Array.from({ length: count }).map((_, index) => (
        <ProductCardSkeleton key={index} />
      ))}
    </div>
  )
}

// Button loading state
export function LoadingButton({ 
  children, 
  isLoading, 
  className = '',
  ...props 
}: {
  children: React.ReactNode
  isLoading: boolean
  className?: string
  [key: string]: any
}) {
  return (
    <button
      className={`relative ${className}`}
      disabled={isLoading}
      {...props}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size="sm" className="text-current" />
        </div>
      )}
      <span className={isLoading ? 'opacity-0' : 'opacity-100'}>
        {children}
      </span>
    </button>
  )
}

// Image loading placeholder
export function ImagePlaceholder({ 
  aspectRatio = 'aspect-[3/4]',
  className = '' 
}: {
  aspectRatio?: string
  className?: string
}) {
  return (
    <div className={`${aspectRatio} bg-gray-200 animate-pulse flex items-center justify-center ${className}`}>
      <svg
        className="h-8 w-8 text-gray-400"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={1}
          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
        />
      </svg>
    </div>
  )
}

// Text skeleton
export function TextSkeleton({ 
  lines = 1, 
  className = '' 
}: { 
  lines?: number
  className?: string 
}) {
  return (
    <div className={`animate-pulse space-y-2 ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={`h-4 bg-gray-200 rounded ${
            index === lines - 1 ? 'w-3/4' : 'w-full'
          }`}
        />
      ))}
    </div>
  )
}

// Category card skeleton
export function CategoryCardSkeleton() {
  return (
    <div className="animate-pulse text-center">
      <div className="aspect-[4/5] bg-gray-200 mb-4"></div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
        <div className="h-3 bg-gray-200 rounded w-1/2 mx-auto"></div>
      </div>
    </div>
  )
}

// Header skeleton (for SSR)
export function HeaderSkeleton() {
  return (
    <div className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20 lg:h-24 xl:h-28">
          <div className="lg:hidden w-6 h-6 bg-gray-200 animate-pulse"></div>
          <div className="h-6 w-24 bg-gray-200 animate-pulse"></div>
          <div className="flex items-center space-x-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="w-6 h-6 bg-gray-200 animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Full page skeleton
export function PageSkeleton() {
  return (
    <div className="min-h-screen">
      <HeaderSkeleton />
      <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-12">
        <div className="space-y-8">
          <TextSkeleton lines={2} className="max-w-md" />
          <ProductGridSkeleton count={8} />
        </div>
      </div>
    </div>
  )
}
