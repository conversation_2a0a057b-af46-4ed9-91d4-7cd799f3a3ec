'use client'

import Link from 'next/link'
import Image from 'next/image'
import { PromotionalCard } from '@/lib/data'

interface PromotionalGridProps {
  cards: PromotionalCard[]
}

export default function PromotionalGrid({ cards }: PromotionalGridProps) {
  const heroCard = cards.find(card => card.id === 'hero')
  const otherCards = cards.filter(card => card.id !== 'hero')

  const getCardClasses = (size: string) => {
    switch (size) {
      case 'large':
        return 'col-span-12 aspect-[16/9] lg:aspect-[21/9]'
      case 'medium':
        return 'col-span-12 md:col-span-6 aspect-[4/3] lg:aspect-[3/2]'
      case 'small':
      default:
        return 'col-span-12 md:col-span-6 lg:col-span-4 aspect-[4/3]'
    }
  }

  const getTextClasses = (position: string = 'bottom-left', color: string = 'white') => {
    const baseClasses = 'absolute z-10'
    const colorClasses = color === 'white' ? 'text-white' : 'text-black'
    
    switch (position) {
      case 'bottom-center':
        return `${baseClasses} bottom-6 left-1/2 transform -translate-x-1/2 text-center ${colorClasses}`
      case 'center':
        return `${baseClasses} top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center ${colorClasses}`
      case 'top-left':
        return `${baseClasses} top-6 left-6 ${colorClasses}`
      case 'bottom-left':
      default:
        return `${baseClasses} bottom-6 left-6 ${colorClasses}`
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12">
      {/* Hero Section */}
      {heroCard && (
        <div className="mb-6 lg:mb-8">
          <Link href={heroCard.link} className="group block">
            <div className="relative aspect-[16/9] lg:aspect-[21/9] overflow-hidden bg-gray-100">
              <Image
                src={heroCard.image}
                alt={heroCard.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-700"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
              <div className={getTextClasses(heroCard.textPosition, heroCard.textColor)}>
                {heroCard.subtitle && (
                  <p className="text-xs lg:text-sm font-light mb-2 tracking-widest uppercase opacity-90">
                    {heroCard.subtitle}
                  </p>
                )}
                <h1 className="text-3xl lg:text-5xl xl:text-6xl font-light mb-4 lg:mb-6 tracking-wide">
                  {heroCard.title}
                </h1>
                <button className="bg-white text-black px-6 lg:px-8 py-2 lg:py-3 text-xs lg:text-sm font-medium tracking-widest hover:bg-gray-100 transition-colors">
                  SHOP
                </button>
              </div>
            </div>
          </Link>
        </div>
      )}

      {/* Promotional Cards Grid */}
      <div className="grid grid-cols-12 gap-4 lg:gap-6 xl:gap-8">
        {otherCards.map((card) => (
          <div key={card.id} className={getCardClasses(card.size)}>
            <Link href={card.link} className="group block h-full">
              <div className="relative h-full overflow-hidden bg-gray-100">
                <Image
                  src={card.image}
                  alt={card.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className={getTextClasses(card.textPosition, card.textColor)}>
                  {card.subtitle && (
                    <p className="text-xs font-light mb-1 lg:mb-2 tracking-widest uppercase opacity-90">
                      {card.subtitle}
                    </p>
                  )}
                  <h3 className="text-sm lg:text-lg xl:text-xl font-light mb-2 lg:mb-3 tracking-wide">
                    {card.title}
                  </h3>
                  {card.description && (
                    <p className="text-xs font-medium tracking-widest uppercase">
                      {card.description}
                    </p>
                  )}
                  {!card.description && (
                    <button className="text-xs font-medium tracking-widest uppercase hover:underline">
                      SHOP
                    </button>
                  )}
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </div>
  )
}
