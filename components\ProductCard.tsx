import Image from 'next/image'
import Link from 'next/link'
import { ProductCardProps } from '@/lib/types'

export default function ProductCard({ product }: ProductCardProps) {
  const formatPrice = (price: string | number) => {
    return typeof price === 'string' ? price : `£${price}`
  }

  return (
    <Link href={`/product/${product.id}`} className="block h-full">
      <article className="product-card bg-white border border-transparent transition-all duration-300 h-full flex flex-col hover:shadow-lg hover:-translate-y-1">
        <div className="relative aspect-[3/4] bg-gray-50 overflow-hidden">
          <Image
            src={product.image}
            alt={product.name}
            fill
            sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
            className="object-cover transition-transform duration-300 group-hover:scale-105"
            priority={false}
          />

          {product.isOnSale && (
            <div className="absolute top-4 left-4 bg-black text-white px-2 py-1 text-xs font-semibold uppercase tracking-wide z-10">
              Sale
            </div>
          )}
        </div>

        <div className="p-6 flex-1 flex flex-col">
          <p className="text-xs text-gray-500 uppercase tracking-wider font-medium mb-1">{product.brand}</p>
          <h3 className="text-sm font-normal text-black line-clamp-2 leading-relaxed mb-4 flex-1">{product.name}</h3>

          <div className="flex items-center gap-2 mt-auto">
            <span className="text-sm font-semibold text-black">{formatPrice(product.price)}</span>
            {product.originalPrice && (
              <span className="text-xs text-gray-400 line-through">{formatPrice(product.originalPrice)}</span>
            )}
          </div>
        </div>
      </article>
    </Link>
  )
}