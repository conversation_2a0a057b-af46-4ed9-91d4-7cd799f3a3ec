
'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useState, useRef } from 'react'
import {
  MagnifyingGlassIcon,
  HeartIcon,
  ShoppingBagIcon,
  UserIcon,
  Bars3Icon,
  XMarkIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'
import { megaMenuData } from '@/lib/data'
import { useIsMobile, useClickOutside, useHoverDelay } from '@/lib/hooks'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [activeMegaMenu, setActiveMegaMenu] = useState<string | null>(null)
  const [expandedMobileMenu, setExpandedMobileMenu] = useState<string | null>(null)

  const megaMenuRef = useRef<HTMLDivElement>(null)
  const isMobile = useIsMobile(768)
  const { setHoverTimeout, clearHoverTimeout } = useHoverDelay()

  useClickOutside(megaMenuRef, () => setActiveMegaMenu(null))

  const handleMegaMenuEnter = (menu: string) => {
    if (!isMobile) {
      clearHoverTimeout()
      setActiveMegaMenu(menu)
    }
  }

  const handleMegaMenuLeave = () => {
    if (!isMobile) {
      setHoverTimeout(() => setActiveMegaMenu(null), 200)
    }
  }

  const handleMegaMenuContentEnter = () => {
    clearHoverTimeout()
  }

  const handleMegaMenuContentLeave = () => {
    if (!isMobile) {
      setHoverTimeout(() => setActiveMegaMenu(null), 100)
    }
  }

  const handleHeaderLeave = () => {
    if (!isMobile) {
      setHoverTimeout(() => setActiveMegaMenu(null), 100)
    }
  }

  const toggleMobileSubmenu = (menu: string) => {
    setExpandedMobileMenu(expandedMobileMenu === menu ? null : menu)
  }

  return (
    <>
      {/* Main Header */}
      <header
        className="bg-white border-b border-gray-200 sticky top-0 z-50"
        ref={megaMenuRef}
        onMouseLeave={handleHeaderLeave}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20 lg:h-24 xl:h-28">
            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 text-black hover:text-gray-600 transition-colors"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <XMarkIcon className="h-5 w-5" /> : <Bars3Icon className="h-5 w-5" />}
            </button>

            {/* Logo */}
            <Link href="/" className="flex-shrink-0">
              <h1 className="text-xl md:text-2xl font-bold tracking-[0.2em] text-black">MATCHES</h1>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-10 xl:space-x-16">
              <div
                className="relative"
                onMouseEnter={() => handleMegaMenuEnter('women')}
                onMouseLeave={handleMegaMenuLeave}
              >
                <Link href="/women" className="text-sm font-medium text-black hover:text-gray-600 transition-colors flex items-center">
                  Women
                  <ChevronDownIcon className="ml-1 h-3 w-3" />
                </Link>
              </div>

              <div
                className="relative"
                onMouseEnter={() => handleMegaMenuEnter('men')}
                onMouseLeave={handleMegaMenuLeave}
              >
                <Link href="/men" className="text-sm font-medium text-black hover:text-gray-600 transition-colors flex items-center">
                  Men
                  <ChevronDownIcon className="ml-1 h-3 w-3" />
                </Link>
              </div>

              <Link href="/designers" className="text-sm font-medium text-black hover:text-gray-600 transition-colors">
                Designers
              </Link>
              <Link href="/sale" className="text-sm font-medium text-red-600 hover:text-red-700 transition-colors">
                Sale
              </Link>
            </nav>

            {/* Right side icons */}
            <div className="flex items-center space-x-4 lg:space-x-6">
              <button
                onClick={() => setIsSearchOpen(!isSearchOpen)}
                className="p-2 text-black hover:text-gray-600 transition-colors lg:flex"
                aria-label="Search"
              >
                <MagnifyingGlassIcon className="h-5 w-5" />
              </button>

              <Link
                href="/account"
                className="p-2 text-black hover:text-gray-600 transition-colors hidden lg:flex"
                aria-label="Account"
              >
                <UserIcon className="h-5 w-5" />
              </Link>

              <Link
                href="/wishlist"
                className="p-2 text-black hover:text-gray-600 transition-colors relative hidden lg:flex"
                aria-label="Wishlist"
              >
                <HeartIcon className="h-5 w-5" />
              </Link>

              <Link
                href="/bag"
                className="p-2 text-black hover:text-gray-600 transition-colors relative"
                aria-label="Shopping bag"
              >
                <ShoppingBagIcon className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* Search Bar */}
          {isSearchOpen && (
            <div className="absolute top-full left-0 right-0 bg-white border-t border-gray-200 py-4 z-50">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex items-center gap-4">
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      placeholder="Search for products, designers..."
                      className="w-full px-4 py-3 border border-gray-300 bg-white text-black focus:outline-none focus:border-black text-sm"
                      autoFocus
                    />
                    <MagnifyingGlassIcon className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  </div>
                  <button
                    onClick={() => setIsSearchOpen(false)}
                    className="text-sm font-medium hover:text-gray-600 text-black px-4"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Rich Visual Mega Menu */}
        {activeMegaMenu && megaMenuData[activeMegaMenu] && (
          <div
            className="absolute top-full left-0 right-0 bg-white border-t border-gray-200 shadow-xl z-40"
            onMouseEnter={handleMegaMenuContentEnter}
            onMouseLeave={handleMegaMenuContentLeave}
          >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
              <div className="grid grid-cols-12 gap-8">
                {/* Featured Section */}
                <div className="col-span-3">
                  <Link
                    href={megaMenuData[activeMegaMenu].featured.link}
                    className="group block"
                  >
                    <div className="relative aspect-[3/4] mb-4 overflow-hidden bg-gray-100">
                      <Image
                        src={megaMenuData[activeMegaMenu].featured.image}
                        alt={megaMenuData[activeMegaMenu].featured.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                      <div className="absolute bottom-6 left-6 text-white">
                        <h3 className="text-lg font-semibold mb-2 tracking-wide">
                          {megaMenuData[activeMegaMenu].featured.title}
                        </h3>
                        <p className="text-sm opacity-90">
                          {megaMenuData[activeMegaMenu].featured.subtitle}
                        </p>
                      </div>
                    </div>
                  </Link>
                </div>

                {/* Categories with Images */}
                <div className="col-span-6">
                  <div className="grid grid-cols-2 gap-8">
                    {megaMenuData[activeMegaMenu].categories.map((category) => (
                      <div key={category.name} className="space-y-4">
                        <Link
                          href={`/${activeMegaMenu}/${category.name.toLowerCase()}`}
                          className="group block"
                        >
                          <div className="relative aspect-[4/3] mb-3 overflow-hidden bg-gray-100">
                            <Image
                              src={category.image}
                              alt={category.name}
                              fill
                              className="object-cover group-hover:scale-105 transition-transform duration-500"
                            />
                          </div>
                          <h3 className="text-sm font-semibold text-black uppercase tracking-wide mb-3 group-hover:text-gray-600 transition-colors">
                            {category.name}
                          </h3>
                        </Link>
                        <div className="space-y-1">
                          {category.items.slice(0, 6).map((item, index) => (
                            <Link
                              key={`${category.name}-${item}-${index}`}
                              href={`/${activeMegaMenu}/${category.name.toLowerCase()}/${item.toLowerCase().replace(/\s+/g, '-')}`}
                              className="block text-xs text-gray-600 hover:text-black transition-colors py-1"
                            >
                              {item}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Editorial & Designers */}
                <div className="col-span-3 space-y-8">
                  {/* Editorial Section */}
                  <div>
                    <Link
                      href={megaMenuData[activeMegaMenu].editorial.link}
                      className="group block"
                    >
                      <div className="relative aspect-[4/3] mb-4 overflow-hidden bg-gray-100">
                        <Image
                          src={megaMenuData[activeMegaMenu].editorial.image}
                          alt={megaMenuData[activeMegaMenu].editorial.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-500"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <div className="absolute bottom-4 left-4 text-white">
                          <h3 className="text-sm font-semibold mb-1 tracking-wide">
                            {megaMenuData[activeMegaMenu].editorial.title}
                          </h3>
                          <p className="text-xs opacity-90">
                            {megaMenuData[activeMegaMenu].editorial.subtitle}
                          </p>
                        </div>
                      </div>
                    </Link>
                  </div>

                  {/* Featured Designers */}
                  <div>
                    <h3 className="text-sm font-semibold text-black uppercase tracking-wide mb-4">
                      Featured Designers
                    </h3>
                    <div className="space-y-2">
                      {megaMenuData[activeMegaMenu].designers.slice(0, 8).map((designer, index) => (
                        <Link
                          key={`${designer}-${index}`}
                          href={`/designers/${designer.toLowerCase().replace(/\s+/g, '-')}`}
                          className="block text-xs text-gray-600 hover:text-black transition-colors py-1"
                        >
                          {designer}
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
              {/* Women Menu */}
              <div className="border-b border-gray-200 pb-4 mb-4">
                <button
                  onClick={() => toggleMobileSubmenu('women')}
                  className="flex items-center justify-between w-full text-left py-3 text-sm font-medium text-black"
                >
                  Women
                  <ChevronDownIcon className={`h-4 w-4 transition-transform ${expandedMobileMenu === 'women' ? 'rotate-180' : ''}`} />
                </button>
                {expandedMobileMenu === 'women' && (
                  <div className="pl-4 space-y-2 mt-2">
                    <Link href="/women/new-in" className="block py-2 text-sm text-gray-600 hover:text-black">New In</Link>
                    <Link href="/women/clothing" className="block py-2 text-sm text-gray-600 hover:text-black">Clothing</Link>
                    <Link href="/women/shoes" className="block py-2 text-sm text-gray-600 hover:text-black">Shoes</Link>
                    <Link href="/women/bags" className="block py-2 text-sm text-gray-600 hover:text-black">Bags</Link>
                    <Link href="/women/accessories" className="block py-2 text-sm text-gray-600 hover:text-black">Accessories</Link>
                  </div>
                )}
              </div>

              {/* Men Menu */}
              <div className="border-b border-gray-200 pb-4 mb-4">
                <button
                  onClick={() => toggleMobileSubmenu('men')}
                  className="flex items-center justify-between w-full text-left py-3 text-sm font-medium text-black"
                >
                  Men
                  <ChevronDownIcon className={`h-4 w-4 transition-transform ${expandedMobileMenu === 'men' ? 'rotate-180' : ''}`} />
                </button>
                {expandedMobileMenu === 'men' && (
                  <div className="pl-4 space-y-2 mt-2">
                    <Link href="/men/new-in" className="block py-2 text-sm text-gray-600 hover:text-black">New In</Link>
                    <Link href="/men/clothing" className="block py-2 text-sm text-gray-600 hover:text-black">Clothing</Link>
                    <Link href="/men/shoes" className="block py-2 text-sm text-gray-600 hover:text-black">Shoes</Link>
                    <Link href="/men/bags" className="block py-2 text-sm text-gray-600 hover:text-black">Bags</Link>
                    <Link href="/men/accessories" className="block py-2 text-sm text-gray-600 hover:text-black">Accessories</Link>
                  </div>
                )}
              </div>

              <div className="space-y-3 border-b border-gray-200 pb-4 mb-4">
                <Link href="/just-in" className="block py-2 text-sm font-medium text-black">Just In</Link>
                <Link href="/designers" className="block py-2 text-sm font-medium text-black">Designers</Link>
                <Link href="/sale" className="block py-2 text-sm font-medium text-red-600">Sale</Link>
              </div>

              <div className="space-y-3">
                <Link href="/account" className="flex items-center py-2 text-sm font-medium text-black">
                  <UserIcon className="h-4 w-4 mr-3" />
                  My Account
                </Link>
                <Link href="/wishlist" className="flex items-center py-2 text-sm font-medium text-black">
                  <HeartIcon className="h-4 w-4 mr-3" />
                  Wishlist
                </Link>
              </div>
            </div>
          </div>
        )}
      </header>
    </>
  )
}
