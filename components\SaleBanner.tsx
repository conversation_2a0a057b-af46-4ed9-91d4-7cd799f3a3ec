
'use client'

import { useState } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

export default function SaleBanner() {
  const [isVisible, setIsVisible] = useState(true)

  if (!isVisible) return null

  return (
    <div className="bg-black text-white relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 lg:py-4">
        <div className="flex items-center justify-center text-center">
          <p className="text-sm md:text-base font-medium">
            <span className="hidden sm:inline">UP TO 70% OFF </span>
            SALE NOW ON
            <span className="hidden sm:inline"> - LIMITED TIME ONLY</span>
            <Link href="/sale" className="ml-2 underline hover:no-underline">
              SHOP NOW
            </Link>
          </p>
        </div>
      </div>
      
      <button
        onClick={() => setIsVisible(false)}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 p-1 hover:bg-white/10 transition-colors"
        aria-label="Close banner"
      >
        <XMarkIcon className="h-4 w-4" />
      </button>
    </div>
  )
}
