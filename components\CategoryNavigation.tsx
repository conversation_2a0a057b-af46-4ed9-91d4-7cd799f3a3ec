'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

interface CategoryNavigationProps {
  categories: Array<{
    name: string
    href: string
    active?: boolean
  }>
}

export default function CategoryNavigation({ categories }: CategoryNavigationProps) {
  const pathname = usePathname()

  return (
    <nav className="border-b border-gray-200 bg-white sticky top-20 lg:top-24 xl:top-28 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-center space-x-8 lg:space-x-12 xl:space-x-16 overflow-x-auto scrollbar-hide">
          {categories.map((category) => {
            const isActive = pathname === category.href || pathname.startsWith(category.href + '/')
            
            return (
              <Link
                key={category.name}
                href={category.href}
                className={`
                  py-4 lg:py-6 text-xs lg:text-sm font-medium tracking-wider whitespace-nowrap
                  border-b-2 transition-colors duration-200
                  ${isActive 
                    ? 'border-black text-black' 
                    : 'border-transparent text-gray-600 hover:text-black hover:border-gray-300'
                  }
                `}
              >
                {category.name}
              </Link>
            )
          })}
        </div>
      </div>
    </nav>
  )
}
