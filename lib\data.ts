// Centralized data management for the Matches Headless UI
import { Product, Category, MegaMenuData, FilterOption } from './types'

// Mock product data - in production this would come from WooCommerce API
export const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Moon oversized cotton-poplin shirt',
    brand: 'The Row',
    price: '£1,200',
    image: '/products/the-row-shirt.jpg',
    isOnSale: false,
    colors: ['White', 'Black', 'Navy'],
    sizes: ['XS', 'S', 'M', 'L', 'XL']
  },
  {
    id: '2',
    name: 'Cashmere sweater',
    brand: '<PERSON><PERSON><PERSON>',
    price: '£890',
    image: '/products/brunello-sweater.jpg',
    isOnSale: false,
    colors: ['Beige', 'Grey', 'Navy']
  },
  {
    id: '3',
    name: 'Wool coat',
    brand: 'Max Mara',
    price: '£1,450',
    originalPrice: '£1,800',
    image: '/products/maxmara-coat.jpg',
    isOnSale: true,
    colors: ['Camel', 'Black', 'Navy']
  },
  {
    id: '4',
    name: 'Silk dress',
    brand: 'Bottega Veneta',
    price: '£2,100',
    image: '/products/bottega-dress.jpg',
    isOnSale: false,
    colors: ['Black', 'Green', 'Brown']
  },
  {
    id: '5',
    name: 'Leather jacket',
    brand: 'Saint Laurent',
    price: '£3,200',
    image: '/products/saint-laurent-jacket.jpg',
    isOnSale: false,
    colors: ['Black']
  },
  {
    id: '6',
    name: 'Knit sweater',
    brand: 'Ganni',
    price: '£295',
    image: '/products/ganni-sweater.jpg',
    isOnSale: false,
    colors: ['Pink', 'Blue', 'Yellow']
  }
]

// Category data
export const categories: Category[] = [
  {
    id: 1,
    name: 'CLOTHING',
    slug: 'clothing',
    count: '545 PRODUCTS',
    image: '/category-clothing.jpg',
    href: '/clothing'
  },
  {
    id: 2,
    name: 'SHOES',
    slug: 'shoes',
    count: '234 PRODUCTS',
    image: '/category-shoes.jpg',
    href: '/shoes'
  },
  {
    id: 3,
    name: 'BAGS',
    slug: 'bags',
    count: '156 PRODUCTS',
    image: '/category-bags.jpg',
    href: '/bags'
  },
  {
    id: 4,
    name: 'ACCESSORIES',
    slug: 'accessories',
    count: '89 PRODUCTS',
    image: '/category-accessories.jpg',
    href: '/accessories'
  },
  {
    id: 5,
    name: 'SALE',
    slug: 'sale',
    count: 'UP TO 70% OFF',
    image: '/category-sale.jpg',
    href: '/sale'
  }
]

// Filter options
export const filterOptions: FilterOption[] = [
  {
    name: 'Categories',
    options: ['Clothing', 'Shoes', 'Bags', 'Accessories']
  },
  {
    name: 'Designer',
    options: ['Bottega Veneta', 'Gucci', 'Prada', 'Saint Laurent', 'The Row', 'Brunello Cucinelli', 'Max Mara', 'Ganni']
  },
  {
    name: 'Colour',
    options: ['Black', 'White', 'Brown', 'Blue', 'Green', 'Red', 'Pink', 'Yellow', 'Beige', 'Grey', 'Navy', 'Camel']
  },
  {
    name: 'Clothes Size',
    options: ['XXS', 'XS', 'S', 'M', 'L', 'XL', 'XXL']
  },
  {
    name: 'Shoe Sizes',
    options: ['5', '6', '7', '8', '9', '10', '11']
  },
  {
    name: 'Shop By',
    options: ['New Arrivals', 'Sale', 'Just In', 'Trending']
  },
  {
    name: 'Price',
    options: ['Under £100', '£100 - £500', '£500 - £1000', 'Over £1000']
  }
]

// Mega menu data
export const megaMenuData: Record<string, MegaMenuData> = {
  women: {
    featured: {
      title: 'NEW SEASON ARRIVALS',
      subtitle: 'Discover the latest from your favorite designers',
      image: '/hero-image.jpg',
      link: '/women/new-arrivals'
    },
    categories: [
      {
        name: 'Clothing',
        image: '/category-clothing.jpg',
        items: ['Shop All', 'Dresses', 'Knitwear', 'Coats', 'Jackets', 'Jeans', 'Jumpsuits', 'Lingerie', 'Skirts', 'Suits', 'Swimwear', 'Tops', 'Trousers']
      },
      {
        name: 'Shoes',
        image: '/category-shoes.jpg',
        items: ['Shop All', 'Boots', 'Flats', 'Heels', 'Sandals', 'Sneakers', 'Trainers']
      },
      {
        name: 'Bags',
        image: '/category-bags.jpg',
        items: ['Shop All', 'Backpacks', 'Belt Bags', 'Clutches', 'Cross-body', 'Handbags', 'Shoulder Bags', 'Totes', 'Travel']
      },
      {
        name: 'Accessories',
        image: '/category-accessories.jpg',
        items: ['Shop All', 'Belts', 'Gloves', 'Hair Accessories', 'Hats', 'Jewelry & Watches', 'Scarves', 'Sunglasses', 'Tech Accessories']
      }
    ],
    editorial: {
      title: 'WINTER ESSENTIALS',
      subtitle: 'Curated edit for the season',
      image: '/editorial-1.jpg',
      link: '/women/winter-essentials'
    },
    designers: ['Acne Studios', 'Bottega Veneta', 'Ganni', 'Jacquemus', 'Khaite', 'Loewe', 'Prada', 'Saint Laurent', 'The Row', 'Toteme']
  },
  men: {
    featured: {
      title: 'NEW SEASON ARRIVALS',
      subtitle: 'Discover the latest from your favorite designers',
      image: '/hero-image.jpg',
      link: '/men/new-arrivals'
    },
    categories: [
      {
        name: 'Clothing',
        image: '/category-clothing.jpg',
        items: ['Shop All', 'Blazers', 'Coats', 'Hoodies', 'Jackets', 'Jeans', 'Knitwear', 'Polo Shirts', 'Shirts', 'Shorts', 'Suits', 'Sweatshirts', 'T-Shirts', 'Trousers']
      },
      {
        name: 'Shoes',
        image: '/category-shoes.jpg',
        items: ['Shop All', 'Boots', 'Dress Shoes', 'Loafers', 'Sandals', 'Sneakers', 'Trainers']
      },
      {
        name: 'Bags',
        image: '/category-bags.jpg',
        items: ['Shop All', 'Backpacks', 'Belt Bags', 'Briefcases', 'Cross-body', 'Messenger', 'Totes', 'Travel']
      },
      {
        name: 'Accessories',
        image: '/category-accessories.jpg',
        items: ['Shop All', 'Belts', 'Gloves', 'Hats', 'Jewelry & Watches', 'Scarves', 'Sunglasses', 'Tech Accessories', 'Ties', 'Wallets']
      }
    ],
    editorial: {
      title: 'MODERN CLASSICS',
      subtitle: 'Timeless pieces for the modern man',
      image: '/editorial-2.jpg',
      link: '/men/modern-classics'
    },
    designers: ['Acne Studios', 'Bottega Veneta', 'Fear of God', 'Jacquemus', 'Kenzo', 'Off-White', 'Stone Island', 'Thom Browne', 'The Row', 'Valentino']
  }
}

// Utility functions
export const formatPrice = (price: string | number): string => {
  if (typeof price === 'string') {
    return price.startsWith('£') ? price : `£${price}`
  }
  return `£${price.toLocaleString()}`
}

export const getProductsByCategory = (categorySlug: string): Product[] => {
  // In production, this would filter products from API
  return mockProducts.filter(product => 
    product.categories?.some(cat => cat.slug === categorySlug)
  )
}

export const getProductById = (id: string): Product | undefined => {
  return mockProducts.find(product => product.id === id)
}

export const searchProducts = (query: string): Product[] => {
  const lowercaseQuery = query.toLowerCase()
  return mockProducts.filter(product =>
    product.name.toLowerCase().includes(lowercaseQuery) ||
    product.brand.toLowerCase().includes(lowercaseQuery)
  )
}
