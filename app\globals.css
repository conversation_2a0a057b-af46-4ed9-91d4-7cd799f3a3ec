
@import "tailwindcss";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

:root {
  /* Custom brand colors only */
  --matches-primary: #000000;
  --matches-secondary: #737373;

  /* Typography */
  --font-body: 'Inter', system-ui, -apple-system, sans-serif;
  --font-display: 'Playfair Display', serif;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-body);
  line-height: 1.6;
}

body {
  color: var(--matches-primary);
  background: white;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.01em;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Essential utilities only */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Focus states */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible {
  outline: 2px solid rgba(0, 0, 0, 0.8);
  outline-offset: 2px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced mobile touch targets */
@media (max-width: 768px) {
  button,
  a[role="button"],
  .touchable {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Improved form elements */
input[type="text"],
input[type="email"],
input[type="search"],
textarea {
  appearance: none;
  -webkit-appearance: none;
}

/* Component-specific styles that can't be replaced with Tailwind */

/* Product card hover effects */
.product-card {
  transition: all 0.3s ease;
}

.product-card:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

/* Accessibility and performance optimizations */

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Performance optimizations */
img {
  content-visibility: auto;
}