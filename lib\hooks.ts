// Custom hooks for the Matches Headless UI
import { useState, useEffect, useCallback } from 'react'
import { Product, SearchFilters } from './types'

// Hook for managing mobile detection
export function useIsMobile(breakpoint: number = 768) {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < breakpoint)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [breakpoint])

  return isMobile
}

// Hook for managing outside clicks
export function useClickOutside(
  ref: React.RefObject<HTMLElement>,
  callback: () => void
) {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        callback()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [ref, callback])
}

// Hook for managing hover delays (useful for mega menus)
export function useHoverDelay() {
  const [timeoutRef, setTimeoutRef] = useState<NodeJS.Timeout | null>(null)

  const setHoverTimeout = useCallback((callback: () => void, delay: number) => {
    if (timeoutRef) {
      clearTimeout(timeoutRef)
    }
    const newTimeout = setTimeout(callback, delay)
    setTimeoutRef(newTimeout)
  }, [timeoutRef])

  const clearHoverTimeout = useCallback(() => {
    if (timeoutRef) {
      clearTimeout(timeoutRef)
      setTimeoutRef(null)
    }
  }, [timeoutRef])

  useEffect(() => {
    return () => {
      if (timeoutRef) {
        clearTimeout(timeoutRef)
      }
    }
  }, [timeoutRef])

  return { setHoverTimeout, clearHoverTimeout }
}

// Hook for managing product filters
export function useProductFilters(products: Product[]) {
  const [filters, setFilters] = useState<SearchFilters>({})
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products)

  const updateFilter = useCallback((key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }, [])

  const clearFilters = useCallback(() => {
    setFilters({})
  }, [])

  useEffect(() => {
    let filtered = [...products]

    // Apply category filter
    if (filters.category) {
      filtered = filtered.filter(product =>
        product.categories?.some(cat => cat.slug === filters.category)
      )
    }

    // Apply brand filter
    if (filters.brand) {
      filtered = filtered.filter(product =>
        product.brand.toLowerCase() === filters.brand?.toLowerCase()
      )
    }

    // Apply color filters
    if (filters.colors && filters.colors.length > 0) {
      filtered = filtered.filter(product =>
        product.colors?.some(color =>
          filters.colors?.includes(typeof color === 'string' ? color : color.name)
        )
      )
    }

    // Apply size filters
    if (filters.sizes && filters.sizes.length > 0) {
      filtered = filtered.filter(product =>
        product.sizes?.some(size => filters.sizes?.includes(size))
      )
    }

    // Apply price range filter
    if (filters.priceRange) {
      const [min, max] = filters.priceRange
      filtered = filtered.filter(product => {
        const price = typeof product.price === 'string' 
          ? parseFloat(product.price.replace(/[£,]/g, ''))
          : product.price
        return price >= min && price <= max
      })
    }

    // Apply sorting
    if (filters.sortBy) {
      switch (filters.sortBy) {
        case 'price-low':
          filtered.sort((a, b) => {
            const priceA = typeof a.price === 'string' 
              ? parseFloat(a.price.replace(/[£,]/g, ''))
              : a.price
            const priceB = typeof b.price === 'string' 
              ? parseFloat(b.price.replace(/[£,]/g, ''))
              : b.price
            return priceA - priceB
          })
          break
        case 'price-high':
          filtered.sort((a, b) => {
            const priceA = typeof a.price === 'string' 
              ? parseFloat(a.price.replace(/[£,]/g, ''))
              : a.price
            const priceB = typeof b.price === 'string' 
              ? parseFloat(b.price.replace(/[£,]/g, ''))
              : b.price
            return priceB - priceA
          })
          break
        case 'newest':
          filtered.sort((a, b) => {
            const dateA = new Date(a.date_created || '').getTime()
            const dateB = new Date(b.date_created || '').getTime()
            return dateB - dateA
          })
          break
        case 'popular':
          // Sort by featured first, then by some popularity metric
          filtered.sort((a, b) => {
            if (a.featured && !b.featured) return -1
            if (!a.featured && b.featured) return 1
            return 0
          })
          break
      }
    }

    setFilteredProducts(filtered)
  }, [products, filters])

  return {
    filters,
    filteredProducts,
    updateFilter,
    clearFilters,
    hasActiveFilters: Object.keys(filters).length > 0
  }
}

// Hook for managing local storage
export function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      if (typeof window !== 'undefined') {
        const item = window.localStorage.getItem(key)
        return item ? JSON.parse(item) : initialValue
      }
      return initialValue
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      setStoredValue(valueToStore)
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore))
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error)
    }
  }, [key, storedValue])

  return [storedValue, setValue] as const
}

// Hook for debouncing values (useful for search)
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}
